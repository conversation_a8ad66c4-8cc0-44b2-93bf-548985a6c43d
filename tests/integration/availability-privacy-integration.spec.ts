import { test } from '@japa/runner'
import { DateTime } from 'luxon'
import User from '#features/user/user_model'
import Task from '#features/task/task_model'
import Event from '#features/event/event_model'
import UserAvailabilitySlot from '#features/availability/user_availability_slot_model'
import AvailabilityService from '#features/availability/availability_service'
import PolicyCategory from '#features/privacy-policy/category/policy_category_model'
import PrivacyPolicyTemplate from '#features/privacy-policy/template/privacy_policy_template_model'
import PolicyTemplateRule from '#features/privacy-policy/rule/policy_template_rule_model'
import PrivacyAssignment from '#models/privacy_assignment'
import UserContact from '#features/contact/user_contact_model'
import UserGroup from '#features/group/user_group_model'
import GroupMember from '#features/group/members/group_member_model'
import UserContactList from '#features/contact/lists/user_contact_list_model'
import ContactListMember from '#features/contact/lists/contact_list_member_model'
import { ActivityDetailVisibilityLevel } from '#types/policy_template_types'

/**
 * Integration Tests: Availability & Privacy Policy System
 * 
 * These tests validate that the availability system correctly integrates
 * with the existing privacy policy system and handles complex scenarios.
 */

test.group('Availability Privacy Integration', (group) => {
  let availabilityService: AvailabilityService
  let user1: User, user2: User, user3: User
  let testDate: DateTime

  group.setup(async () => {
    availabilityService = new AvailabilityService()
    testDate = DateTime.fromISO('2025-01-15T10:00:00Z')
  })

  group.each.setup(async () => {
    user1 = await User.create({ email: '<EMAIL>', password: 'password' })
    user2 = await User.create({ email: '<EMAIL>', password: 'password' })
    user3 = await User.create({ email: '<EMAIL>', password: 'password' })
  })

  /**
   * Test privacy rule priority system
   * Critical: Ensures highest priority rule wins when multiple rules match
   */
  test('privacy rules are applied by priority when multiple rules match', async ({ assert }) => {
    // Setup: Create contact relationship
    await UserContact.create({
      userId: user1.id,
      contactUserId: user2.id,
      status: 'accepted',
    })

    // Setup: Create group relationship
    const group = await UserGroup.create({
      userId: user1.id,
      name: 'Test Group',
    })

    await GroupMember.create({
      groupId: group.id,
      userId: user2.id,
      role: 'member',
    })

    // Setup: Privacy template with multiple rules
    const template = await PrivacyPolicyTemplate.create({
      userId: user1.id,
      name: 'Multi-Rule Template',
      blocksScheduling: true,
      defaultDetailVisibility: ActivityDetailVisibilityLevel.HIDDEN,
    })

    // Rule 1: All contacts see busy only (priority 1 - lower)
    await PolicyTemplateRule.create({
      userId: user1.id,
      templateId: template.id,
      viewerScopeType: 'allContacts',
      detailVisibility: ActivityDetailVisibilityLevel.BUSY_ONLY,
      blocksScheduling: true,
      priority: 1,
    })

    // Rule 2: Specific group sees full details (priority 2 - higher)
    await PolicyTemplateRule.create({
      userId: user1.id,
      templateId: template.id,
      viewerScopeType: 'specificGroup',
      viewerTargetGroupId: group.id,
      detailVisibility: ActivityDetailVisibilityLevel.FULL_DETAILS,
      blocksScheduling: false,
      priority: 2,
    })

    // Setup: Task with privacy assignment
    const task = await Task.create({
      userId: user1.id,
      title: 'Important Meeting',
      description: 'Confidential project discussion',
      startDate: testDate.toJSDate(),
      endDate: testDate.plus({ hours: 1 }).toJSDate(),
    })

    await PrivacyAssignment.create({
      taskId: task.id,
      templateId: template.id,
    })

    // Test: User2 is both contact AND group member - higher priority rule should win
    const availability = await availabilityService.getAvailabilityForDay(
      user2.id,
      user1.id,
      testDate
    )

    assert.equal(availability.activities.length, 1)
    const activity = availability.activities[0]
    
    // Should use group rule (priority 2) not contact rule (priority 1)
    assert.equal(activity.title, 'Important Meeting', 'Should show full title from group rule')
    assert.equal(activity.description, 'Confidential project discussion', 'Should show description from group rule')
    assert.isFalse(activity.blocksScheduling, 'Should use group rule scheduling permission')
  })

  /**
   * Test privacy assignment overrides
   * Critical: Activity-level overrides must take precedence over template defaults
   */
  test('privacy assignment overrides work correctly', async ({ assert }) => {
    // Setup: Contact relationship
    await UserContact.create({
      userId: user1.id,
      contactUserId: user2.id,
      status: 'accepted',
    })

    // Setup: Template with default settings
    const template = await PrivacyPolicyTemplate.create({
      userId: user1.id,
      name: 'Template with Overrides',
      blocksScheduling: false,
      defaultDetailVisibility: ActivityDetailVisibilityLevel.TITLE_ONLY,
    })

    await PolicyTemplateRule.create({
      userId: user1.id,
      templateId: template.id,
      viewerScopeType: 'allContacts',
      detailVisibility: ActivityDetailVisibilityLevel.TITLE_ONLY,
      blocksScheduling: false,
      priority: 1,
    })

    // Setup: Task with privacy assignment that overrides template
    const task = await Task.create({
      userId: user1.id,
      title: 'Regular Meeting',
      description: 'Weekly team sync',
      startDate: testDate.toJSDate(),
      endDate: testDate.plus({ hours: 1 }).toJSDate(),
    })

    await PrivacyAssignment.create({
      taskId: task.id,
      templateId: template.id,
      // Override template settings
      overrideDetailVisibility: ActivityDetailVisibilityLevel.BUSY_ONLY,
      overrideBlocksScheduling: true,
      overrideCustomMessage: 'In a meeting',
    })

    // Test: Overrides should take precedence
    const availability = await availabilityService.getAvailabilityForDay(
      user2.id,
      user1.id,
      testDate
    )

    assert.equal(availability.activities.length, 1)
    const activity = availability.activities[0]
    
    assert.equal(activity.title, 'In a meeting', 'Should use override custom message')
    assert.isNull(activity.description, 'Should hide description due to override')
    assert.isTrue(activity.blocksScheduling, 'Should use override scheduling setting')
  })

  /**
   * Test complex relationship context scenarios
   * Critical: Handles users with multiple relationship types correctly
   */
  test('handles complex relationship contexts correctly', async ({ assert }) => {
    // Setup: User2 is contact, group member, AND in contact list
    await UserContact.create({
      userId: user1.id,
      contactUserId: user2.id,
      status: 'accepted',
    })

    const group = await UserGroup.create({
      userId: user1.id,
      name: 'Work Team',
    })

    await GroupMember.create({
      groupId: group.id,
      userId: user2.id,
      role: 'member',
    })

    const contactList = await UserContactList.create({
      userId: user1.id,
      name: 'VIP Contacts',
    })

    await ContactListMember.create({
      contactListId: contactList.id,
      contactUserId: user2.id,
    })

    // Setup: Template with rules for each relationship type
    const template = await PrivacyPolicyTemplate.create({
      userId: user1.id,
      name: 'Complex Relationship Template',
      blocksScheduling: true,
      defaultDetailVisibility: ActivityDetailVisibilityLevel.HIDDEN,
    })

    // Rule 1: Contact list gets full details (priority 3 - highest)
    await PolicyTemplateRule.create({
      userId: user1.id,
      templateId: template.id,
      viewerScopeType: 'specificContactList',
      viewerTargetContactListId: contactList.id,
      detailVisibility: ActivityDetailVisibilityLevel.FULL_DETAILS,
      blocksScheduling: false,
      priority: 3,
    })

    // Rule 2: Group gets title only (priority 2)
    await PolicyTemplateRule.create({
      userId: user1.id,
      templateId: template.id,
      viewerScopeType: 'specificGroup',
      viewerTargetGroupId: group.id,
      detailVisibility: ActivityDetailVisibilityLevel.TITLE_ONLY,
      blocksScheduling: true,
      priority: 2,
    })

    // Rule 3: All contacts get busy only (priority 1 - lowest)
    await PolicyTemplateRule.create({
      userId: user1.id,
      templateId: template.id,
      viewerScopeType: 'allContacts',
      detailVisibility: ActivityDetailVisibilityLevel.BUSY_ONLY,
      blocksScheduling: true,
      priority: 1,
    })

    // Setup: Task with privacy assignment
    const task = await Task.create({
      userId: user1.id,
      title: 'Strategic Planning',
      description: 'Q1 strategy session with leadership team',
      startDate: testDate.toJSDate(),
      endDate: testDate.plus({ hours: 2 }).toJSDate(),
    })

    await PrivacyAssignment.create({
      taskId: task.id,
      templateId: template.id,
    })

    // Test: Should use highest priority rule (contact list)
    const availability = await availabilityService.getAvailabilityForDay(
      user2.id,
      user1.id,
      testDate
    )

    assert.equal(availability.activities.length, 1)
    const activity = availability.activities[0]
    
    assert.equal(activity.title, 'Strategic Planning', 'Should show full title from contact list rule')
    assert.equal(activity.description, 'Q1 strategy session with leadership team', 'Should show description from contact list rule')
    assert.isFalse(activity.blocksScheduling, 'Should use contact list rule scheduling permission')
  })

  /**
   * Test default behavior when no privacy assignment exists
   * Critical: System must handle activities without explicit privacy assignments
   */
  test('applies default privacy behavior when no assignment exists', async ({ assert }) => {
    // Setup: Contact relationship
    await UserContact.create({
      userId: user1.id,
      contactUserId: user2.id,
      status: 'accepted',
    })

    // Setup: Task WITHOUT privacy assignment
    const task = await Task.create({
      userId: user1.id,
      title: 'Unassigned Task',
      description: 'Task without privacy template',
      startDate: testDate.toJSDate(),
      endDate: testDate.plus({ hours: 1 }).toJSDate(),
    })

    // Test: Should apply default behavior (busy only, blocks scheduling)
    const availability = await availabilityService.getAvailabilityForDay(
      user2.id,
      user1.id,
      testDate
    )

    assert.equal(availability.activities.length, 1)
    const activity = availability.activities[0]
    
    assert.equal(activity.title, 'Busy', 'Should show default busy message')
    assert.isNull(activity.description, 'Should hide description by default')
    assert.isTrue(activity.blocksScheduling, 'Should block scheduling by default')
  })

  /**
   * Test availability slot visibility rules
   * Critical: General availability slots must respect their own visibility settings
   */
  test('availability slots respect visibility rules', async ({ assert }) => {
    // Setup: Contact relationship
    await UserContact.create({
      userId: user1.id,
      contactUserId: user2.id,
      status: 'accepted',
    })

    // Setup: Group relationship
    const group = await UserGroup.create({
      userId: user1.id,
      name: 'Close Friends',
    })

    await GroupMember.create({
      groupId: group.id,
      userId: user2.id,
      role: 'member',
    })

    // Setup: Different visibility availability slots
    await UserAvailabilitySlot.create({
      userId: user1.id,
      title: 'Public Office Hours',
      dayOfWeek: testDate.weekday,
      startTime: '09:00',
      endTime: '12:00',
      isAvailable: true,
      visibilityScope: 'public',
    })

    await UserAvailabilitySlot.create({
      userId: user1.id,
      title: 'Contact-Only Availability',
      dayOfWeek: testDate.weekday,
      startTime: '13:00',
      endTime: '15:00',
      isAvailable: true,
      visibilityScope: 'all_contacts',
    })

    await UserAvailabilitySlot.create({
      userId: user1.id,
      title: 'Group-Only Availability',
      dayOfWeek: testDate.weekday,
      startTime: '15:00',
      endTime: '17:00',
      isAvailable: true,
      visibilityScope: 'specific_group',
      visibilityTargetGroupId: group.id,
    })

    await UserAvailabilitySlot.create({
      userId: user1.id,
      title: 'Private Availability',
      dayOfWeek: testDate.weekday,
      startTime: '17:00',
      endTime: '19:00',
      isAvailable: true,
      visibilityScope: 'specific_contact',
      visibilityTargetUserId: user3.id, // Different user
    })

    // Test: User2 (contact + group member) should see public, contact, and group slots
    const availability = await availabilityService.getAvailabilityForDay(
      user2.id,
      user1.id,
      testDate
    )

    assert.equal(availability.generalAvailability.length, 3, 'Should see 3 out of 4 slots')
    
    const slotTitles = availability.generalAvailability.map(slot => slot.title)
    assert.include(slotTitles, 'Public Office Hours')
    assert.include(slotTitles, 'Contact-Only Availability')
    assert.include(slotTitles, 'Group-Only Availability')
    assert.notInclude(slotTitles, 'Private Availability')

    // Test: User3 (no relationship) should only see public slots
    const publicAvailability = await availabilityService.getAvailabilityForDay(
      user3.id,
      user1.id,
      testDate
    )

    assert.equal(publicAvailability.generalAvailability.length, 1, 'Should only see public slot')
    assert.equal(publicAvailability.generalAvailability[0].title, 'Public Office Hours')
  })

  /**
   * Test privacy rule evaluation with no matching rules
   * Critical: Must handle cases where no rules match the viewer
   */
  test('handles cases where no privacy rules match the viewer', async ({ assert }) => {
    // Setup: Template with specific rules that won't match user2
    const template = await PrivacyPolicyTemplate.create({
      userId: user1.id,
      name: 'Restrictive Template',
      blocksScheduling: false,
      defaultDetailVisibility: ActivityDetailVisibilityLevel.TITLE_ONLY,
      defaultCustomMessage: 'Default busy message',
    })

    // Rule only applies to user3, not user2
    await PolicyTemplateRule.create({
      userId: user1.id,
      templateId: template.id,
      viewerScopeType: 'specificContact',
      viewerTargetUserId: user3.id,
      detailVisibility: ActivityDetailVisibilityLevel.FULL_DETAILS,
      blocksScheduling: false,
      priority: 1,
    })

    // Setup: Task with privacy assignment
    const task = await Task.create({
      userId: user1.id,
      title: 'Restricted Meeting',
      description: 'Only specific people can see this',
      startDate: testDate.toJSDate(),
      endDate: testDate.plus({ hours: 1 }).toJSDate(),
    })

    await PrivacyAssignment.create({
      taskId: task.id,
      templateId: template.id,
    })

    // Test: User2 doesn't match any rules, should get template defaults
    const availability = await availabilityService.getAvailabilityForDay(
      user2.id,
      user1.id,
      testDate
    )

    assert.equal(availability.activities.length, 1)
    const activity = availability.activities[0]
    
    assert.equal(activity.title, 'Restricted Meeting', 'Should show title from template default')
    assert.isNull(activity.description, 'Should hide description per template default')
    assert.isFalse(activity.blocksScheduling, 'Should use template default scheduling')
  })
})
