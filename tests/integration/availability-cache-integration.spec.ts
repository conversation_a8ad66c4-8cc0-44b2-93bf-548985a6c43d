import { test } from '@japa/runner'
import { DateTime } from 'luxon'
import redis from '@adonisjs/redis/services/main'
import User from '#features/user/user_model'
import Task from '#features/task/task_model'
import UserAvailabilitySlot from '#features/availability/user_availability_slot_model'
import AvailabilityService from '#features/availability/availability_service'
import AvailabilityCacheService from '#features/availability/availability_cache_service'
import UserContact from '#features/contact/user_contact_model'
import PrivacyPolicyTemplate from '#features/privacy-policy/template/privacy_policy_template_model'
import PolicyTemplateRule from '#features/privacy-policy/rule/policy_template_rule_model'
import PrivacyAssignment from '#models/privacy_assignment'
import { ActivityDetailVisibilityLevel } from '#types/policy_template_types'

/**
 * Integration Tests: Availability Cache System
 * 
 * These tests validate cache consistency, performance, and invalidation
 * strategies for the availability system.
 */

test.group('Availability Cache Integration', (group) => {
  let availabilityService: AvailabilityService
  let cacheService: AvailabilityCacheService
  let user1: User, user2: User
  let testDate: DateTime

  group.setup(async () => {
    availabilityService = new AvailabilityService()
    cacheService = new AvailabilityCacheService()
    testDate = DateTime.fromISO('2025-01-15T10:00:00Z')
  })

  group.each.setup(async () => {
    // Clear Redis cache before each test
    await redis.flushall()
    
    user1 = await User.create({ email: '<EMAIL>', password: 'password' })
    user2 = await User.create({ email: '<EMAIL>', password: 'password' })
  })

  group.each.teardown(async () => {
    // Clean up Redis after each test
    await redis.flushall()
  })

  /**
   * Test relationship context caching
   * Critical: Relationship context should be cached and reused
   */
  test('relationship context is cached and reused correctly', async ({ assert }) => {
    // Setup: Create contact relationship
    await UserContact.create({
      userId: user1.id,
      contactUserId: user2.id,
      status: 'accepted',
    })

    // First call - should cache the relationship context
    const availability1 = await availabilityService.getAvailabilityForDay(
      user2.id,
      user1.id,
      testDate
    )

    assert.isFalse(availability1.requesterContext.fromCache, 'First call should not be from cache')
    assert.isTrue(availability1.requesterContext.isContact, 'Should detect contact relationship')

    // Second call - should use cached relationship context
    const availability2 = await availabilityService.getAvailabilityForDay(
      user2.id,
      user1.id,
      testDate
    )

    assert.isTrue(availability2.requesterContext.fromCache, 'Second call should be from cache')
    assert.isTrue(availability2.requesterContext.isContact, 'Cached context should be correct')

    // Verify cache key exists in Redis
    const cacheKey = `relationship:${user2.id}:${user1.id}`
    const cachedData = await redis.get(cacheKey)
    assert.exists(cachedData, 'Relationship context should be cached in Redis')
    
    const parsedData = JSON.parse(cachedData)
    assert.isTrue(parsedData.isContact, 'Cached data should be correct')
  })

  /**
   * Test activity data caching
   * Critical: Activity queries should be cached for performance
   */
  test('activity data is cached and invalidated correctly', async ({ assert }) => {
    // Setup: Create task
    const task = await Task.create({
      userId: user1.id,
      title: 'Test Task',
      description: 'Test description',
      startDate: testDate.toJSDate(),
      endDate: testDate.plus({ hours: 1 }).toJSDate(),
    })

    // First call - should cache activities
    const availability1 = await availabilityService.getAvailabilityForDay(
      user2.id,
      user1.id,
      testDate
    )

    assert.equal(availability1.activities.length, 1)
    assert.equal(availability1.activities[0].title, 'Busy') // Default privacy

    // Verify activity cache exists
    const activityCacheKey = `activities:${user1.id}:${testDate.toISODate()}`
    const cachedActivities = await redis.get(activityCacheKey)
    assert.exists(cachedActivities, 'Activities should be cached')

    // Update task title
    task.title = 'Updated Task'
    await task.save()

    // Invalidate activity cache
    await availabilityService.invalidateActivityCaches(user1.id, testDate)

    // Verify cache was invalidated
    const invalidatedCache = await redis.get(activityCacheKey)
    assert.isNull(invalidatedCache, 'Activity cache should be invalidated')

    // Next call should fetch fresh data
    const availability2 = await availabilityService.getAvailabilityForDay(
      user2.id,
      user1.id,
      testDate
    )

    // Should have fresh data (though still shows as "Busy" due to privacy)
    assert.equal(availability2.activities.length, 1)
  })

  /**
   * Test availability slot caching
   * Critical: Availability slots should be cached and invalidated properly
   */
  test('availability slots are cached and invalidated correctly', async ({ assert }) => {
    // Setup: Create availability slot
    await UserAvailabilitySlot.create({
      userId: user1.id,
      title: 'Work Hours',
      dayOfWeek: testDate.weekday,
      startTime: '09:00',
      endTime: '17:00',
      isAvailable: true,
      visibilityScope: 'public',
    })

    // First call - should cache availability slots
    const availability1 = await availabilityService.getAvailabilityForDay(
      user2.id,
      user1.id,
      testDate
    )

    assert.equal(availability1.generalAvailability.length, 1)
    assert.equal(availability1.generalAvailability[0].title, 'Work Hours')

    // Verify availability cache exists
    const availabilityCacheKey = `availability_slots:${user1.id}:${testDate.toISODate()}`
    const cachedSlots = await redis.get(availabilityCacheKey)
    assert.exists(cachedSlots, 'Availability slots should be cached')

    // Invalidate availability cache
    await availabilityService.invalidateAvailabilitySlotCaches(user1.id)

    // Verify cache was invalidated
    const invalidatedCache = await redis.get(availabilityCacheKey)
    assert.isNull(invalidatedCache, 'Availability cache should be invalidated')
  })

  /**
   * Test batch caching for date ranges
   * Critical: Calendar view queries should cache individual days
   */
  test('batch date range queries cache individual days correctly', async ({ assert }) => {
    // Setup: Create tasks across multiple days
    const startDate = testDate.startOf('day')
    const endDate = startDate.plus({ days: 2 })

    await Task.create({
      userId: user1.id,
      title: 'Day 1 Task',
      startDate: startDate.toJSDate(),
      endDate: startDate.plus({ hours: 1 }).toJSDate(),
    })

    await Task.create({
      userId: user1.id,
      title: 'Day 2 Task',
      startDate: startDate.plus({ days: 1 }).toJSDate(),
      endDate: startDate.plus({ days: 1, hours: 1 }).toJSDate(),
    })

    // Batch query should cache individual days
    const batchAvailability = await availabilityService.getAvailabilityForDateRange(
      user2.id,
      user1.id,
      startDate,
      endDate
    )

    assert.equal(batchAvailability.length, 3, 'Should return 3 days')

    // Verify individual day caches were created
    for (let i = 0; i < 3; i++) {
      const date = startDate.plus({ days: i })
      const cacheKey = `activities:${user1.id}:${date.toISODate()}`
      const cachedData = await redis.get(cacheKey)
      assert.exists(cachedData, `Day ${i + 1} should be cached`)
    }

    // Single day query should now hit cache
    const singleDayAvailability = await availabilityService.getAvailabilityForDay(
      user2.id,
      user1.id,
      startDate
    )

    // Should have the same data as from batch query
    assert.equal(singleDayAvailability.activities.length, 1)
  })

  /**
   * Test cache invalidation when relationships change
   * Critical: Relationship changes should invalidate relevant caches
   */
  test('relationship changes invalidate relevant caches', async ({ assert }) => {
    // Setup: Initial state without contact relationship
    const availability1 = await availabilityService.getAvailabilityForDay(
      user2.id,
      user1.id,
      testDate
    )

    assert.isFalse(availability1.requesterContext.isContact, 'Should not be contact initially')

    // Create contact relationship
    await UserContact.create({
      userId: user1.id,
      contactUserId: user2.id,
      status: 'accepted',
    })

    // Invalidate relationship cache
    await availabilityService.invalidateRelationshipCaches(user1.id)

    // Next query should detect new relationship
    const availability2 = await availabilityService.getAvailabilityForDay(
      user2.id,
      user1.id,
      testDate
    )

    assert.isFalse(availability2.requesterContext.fromCache, 'Should fetch fresh relationship data')
    assert.isTrue(availability2.requesterContext.isContact, 'Should detect new contact relationship')
  })

  /**
   * Test cache performance under concurrent load
   * Critical: Cache should handle concurrent requests without corruption
   */
  test('cache handles concurrent requests correctly', async ({ assert }) => {
    // Setup: Create contact relationship and task
    await UserContact.create({
      userId: user1.id,
      contactUserId: user2.id,
      status: 'accepted',
    })

    const template = await PrivacyPolicyTemplate.create({
      userId: user1.id,
      name: 'Test Template',
      blocksScheduling: false,
      defaultDetailVisibility: ActivityDetailVisibilityLevel.TITLE_ONLY,
    })

    await PolicyTemplateRule.create({
      userId: user1.id,
      templateId: template.id,
      viewerScopeType: 'allContacts',
      detailVisibility: ActivityDetailVisibilityLevel.FULL_DETAILS,
      blocksScheduling: false,
      priority: 1,
    })

    const task = await Task.create({
      userId: user1.id,
      title: 'Concurrent Test Task',
      description: 'Testing concurrent access',
      startDate: testDate.toJSDate(),
      endDate: testDate.plus({ hours: 1 }).toJSDate(),
    })

    await PrivacyAssignment.create({
      taskId: task.id,
      templateId: template.id,
    })

    // Make 10 concurrent requests
    const concurrentPromises = Array.from({ length: 10 }, () =>
      availabilityService.getAvailabilityForDay(user2.id, user1.id, testDate)
    )

    const results = await Promise.all(concurrentPromises)

    // All results should be consistent
    results.forEach((result, index) => {
      assert.equal(result.activities.length, 1, `Request ${index + 1} should have 1 activity`)
      assert.equal(result.activities[0].title, 'Concurrent Test Task', `Request ${index + 1} should have correct title`)
      assert.equal(result.activities[0].description, 'Testing concurrent access', `Request ${index + 1} should have correct description`)
    })

    // At least some requests should have hit the cache
    const cacheHits = results.filter(r => r.requesterContext.fromCache).length
    assert.isAbove(cacheHits, 0, 'Some requests should have hit the cache')
  })

  /**
   * Test cache TTL and expiration
   * Critical: Cache should expire according to TTL settings
   */
  test('cache respects TTL settings', async ({ assert }) => {
    // Setup: Create relationship
    await UserContact.create({
      userId: user1.id,
      contactUserId: user2.id,
      status: 'accepted',
    })

    // Make initial request to populate cache
    await availabilityService.getAvailabilityForDay(user2.id, user1.id, testDate)

    // Check that cache keys have TTL set
    const relationshipKey = `relationship:${user2.id}:${user1.id}`
    const activityKey = `activities:${user1.id}:${testDate.toISODate()}`

    const relationshipTTL = await redis.ttl(relationshipKey)
    const activityTTL = await redis.ttl(activityKey)

    assert.isAbove(relationshipTTL, 0, 'Relationship cache should have TTL set')
    assert.isAbove(activityTTL, 0, 'Activity cache should have TTL set')

    // Relationship cache should have longer TTL than activity cache
    assert.isAbove(relationshipTTL, activityTTL, 'Relationship cache should have longer TTL')
  })

  /**
   * Test cache statistics and monitoring
   * Critical: Cache service should provide monitoring capabilities
   */
  test('cache service provides accurate statistics', async ({ assert }) => {
    // Setup: Create some cached data
    await UserContact.create({
      userId: user1.id,
      contactUserId: user2.id,
      status: 'accepted',
    })

    await Task.create({
      userId: user1.id,
      title: 'Stats Test Task',
      startDate: testDate.toJSDate(),
      endDate: testDate.plus({ hours: 1 }).toJSDate(),
    })

    await UserAvailabilitySlot.create({
      userId: user1.id,
      title: 'Stats Test Slot',
      dayOfWeek: testDate.weekday,
      startTime: '09:00',
      endTime: '17:00',
      isAvailable: true,
      visibilityScope: 'public',
    })

    // Make requests to populate cache
    await availabilityService.getAvailabilityForDay(user2.id, user1.id, testDate)

    // Get cache statistics
    const stats = await availabilityService.getCacheStats()

    assert.isAbove(stats.relationshipCacheSize, 0, 'Should have relationship cache entries')
    assert.isAbove(stats.activityCacheSize, 0, 'Should have activity cache entries')
    assert.isAbove(stats.availabilityCacheSize, 0, 'Should have availability cache entries')
    assert.exists(stats.totalMemoryUsage, 'Should report memory usage')
  })

  /**
   * Test graceful degradation when Redis is unavailable
   * Critical: System should continue working if cache fails
   */
  test('system works gracefully when cache is unavailable', async ({ assert }) => {
    // Setup: Create test data
    await UserContact.create({
      userId: user1.id,
      contactUserId: user2.id,
      status: 'accepted',
    })

    await Task.create({
      userId: user1.id,
      title: 'Graceful Degradation Test',
      startDate: testDate.toJSDate(),
      endDate: testDate.plus({ hours: 1 }).toJSDate(),
    })

    // Simulate Redis failure by disconnecting
    await redis.disconnect()

    // System should still work without cache
    const availability = await availabilityService.getAvailabilityForDay(
      user2.id,
      user1.id,
      testDate
    )

    assert.equal(availability.activities.length, 1, 'Should still return activities')
    assert.equal(availability.activities[0].title, 'Busy', 'Should apply default privacy')
    assert.isFalse(availability.requesterContext.fromCache, 'Should not be from cache')

    // Reconnect Redis for cleanup
    await redis.connect()
  })
})
