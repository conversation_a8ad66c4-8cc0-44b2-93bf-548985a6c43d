import { test } from '@japa/runner'
import { DateTime } from 'luxon'
import User from '#features/user/user_model'
import UserAvailabilitySlot from '#features/availability/user_availability_slot_model'
import UserAvailabilitySlotService from '#features/availability/user_availability_slot_service'
import { AvailabilityScopeType } from '#types/policy_template_types'

/**
 * Unit Tests: UserAvailabilitySlot Model and Service
 * 
 * These tests validate individual component behavior, validation logic,
 * and business rules for availability slots.
 */

test.group('UserAvailabilitySlot Model', (group) => {
  let user: User

  group.each.setup(async () => {
    user = await User.create({
      email: '<EMAIL>',
      password: 'password123',
    })
  })

  /**
   * Test computed properties
   */
  test('computed properties work correctly', async ({ assert }) => {
    // Test recurring slot
    const recurringSlot = await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'Work Hours',
      dayOfWeek: 1, // Monday
      startTime: '09:00',
      endTime: '17:00',
      isAvailable: true,
      visibilityScope: 'public',
    })

    assert.isTrue(recurringSlot.isRecurring, 'Should be identified as recurring')
    assert.isFalse(recurringSlot.isSpecific, 'Should not be identified as specific')
    assert.equal(recurringSlot.slotType, 'recurring', 'Should have correct slot type')
    assert.equal(recurringSlot.dayName, 'Monday', 'Should return correct day name')

    // Test specific slot
    const specificSlot = await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'Vacation Day',
      specificStartDate: DateTime.fromISO('2025-01-15T00:00:00Z'),
      specificEndDate: DateTime.fromISO('2025-01-15T23:59:59Z'),
      isAvailable: false,
      visibilityScope: 'public',
    })

    assert.isFalse(specificSlot.isRecurring, 'Should not be identified as recurring')
    assert.isTrue(specificSlot.isSpecific, 'Should be identified as specific')
    assert.equal(specificSlot.slotType, 'specific', 'Should have correct slot type')
    assert.isNull(specificSlot.dayName, 'Should return null for specific slots')
  })

  /**
   * Test validation helpers
   */
  test('validation helpers work correctly', async ({ assert }) => {
    // Valid recurring slot
    const validRecurring = await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'Valid Recurring',
      dayOfWeek: 2,
      startTime: '09:00',
      endTime: '17:00',
      isAvailable: true,
      visibilityScope: 'public',
    })

    assert.isTrue(validRecurring.validateSlotType(), 'Valid recurring slot should pass validation')
    assert.isTrue(validRecurring.validateTimeOrder(), 'Valid time order should pass validation')

    // Valid specific slot
    const validSpecific = await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'Valid Specific',
      specificStartDate: DateTime.fromISO('2025-01-15T09:00:00Z'),
      specificEndDate: DateTime.fromISO('2025-01-15T17:00:00Z'),
      isAvailable: true,
      visibilityScope: 'public',
    })

    assert.isTrue(validSpecific.validateSlotType(), 'Valid specific slot should pass validation')
    assert.isTrue(validSpecific.validateTimeOrder(), 'Valid time order should pass validation')
  })

  /**
   * Test isActiveOnDate method
   */
  test('isActiveOnDate works correctly for recurring slots', async ({ assert }) => {
    const recurringSlot = await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'Monday Work Hours',
      dayOfWeek: 1, // Monday
      startTime: '09:00',
      endTime: '17:00',
      recurringStartDate: DateTime.fromISO('2025-01-01'),
      recurringEndDate: DateTime.fromISO('2025-12-31'),
      isAvailable: true,
      visibilityScope: 'public',
    })

    // Test Monday (should be active)
    const monday = DateTime.fromISO('2025-01-13') // Monday
    assert.isTrue(recurringSlot.isActiveOnDate(monday), 'Should be active on Monday')

    // Test Tuesday (should not be active)
    const tuesday = DateTime.fromISO('2025-01-14') // Tuesday
    assert.isFalse(recurringSlot.isActiveOnDate(tuesday), 'Should not be active on Tuesday')

    // Test Monday before start date (should not be active)
    const earlyMonday = DateTime.fromISO('2024-12-30') // Monday before start date
    assert.isFalse(recurringSlot.isActiveOnDate(earlyMonday), 'Should not be active before start date')

    // Test Monday after end date (should not be active)
    const lateMonday = DateTime.fromISO('2026-01-05') // Monday after end date
    assert.isFalse(recurringSlot.isActiveOnDate(lateMonday), 'Should not be active after end date')
  })

  test('isActiveOnDate works correctly for specific slots', async ({ assert }) => {
    const specificSlot = await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'Vacation Day',
      specificStartDate: DateTime.fromISO('2025-01-15T00:00:00Z'),
      specificEndDate: DateTime.fromISO('2025-01-15T23:59:59Z'),
      isAvailable: false,
      visibilityScope: 'public',
    })

    // Test the specific date (should be active)
    const specificDate = DateTime.fromISO('2025-01-15')
    assert.isTrue(specificSlot.isActiveOnDate(specificDate), 'Should be active on specific date')

    // Test different date (should not be active)
    const differentDate = DateTime.fromISO('2025-01-16')
    assert.isFalse(specificSlot.isActiveOnDate(differentDate), 'Should not be active on different date')
  })

  /**
   * Test getTimeRangeForDate method
   */
  test('getTimeRangeForDate works correctly', async ({ assert }) => {
    // Recurring slot
    const recurringSlot = await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'Work Hours',
      dayOfWeek: 1, // Monday
      startTime: '09:00',
      endTime: '17:00',
      isAvailable: true,
      visibilityScope: 'public',
    })

    const monday = DateTime.fromISO('2025-01-13') // Monday
    const timeRange = recurringSlot.getTimeRangeForDate(monday)

    assert.exists(timeRange, 'Should return time range for active date')
    assert.equal(timeRange!.start.hour, 9, 'Should have correct start hour')
    assert.equal(timeRange!.start.minute, 0, 'Should have correct start minute')
    assert.equal(timeRange!.end.hour, 17, 'Should have correct end hour')
    assert.equal(timeRange!.end.minute, 0, 'Should have correct end minute')

    // Test inactive date
    const tuesday = DateTime.fromISO('2025-01-14') // Tuesday
    const noTimeRange = recurringSlot.getTimeRangeForDate(tuesday)
    assert.isNull(noTimeRange, 'Should return null for inactive date')

    // Specific slot
    const specificSlot = await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'Meeting',
      specificStartDate: DateTime.fromISO('2025-01-15T14:00:00Z'),
      specificEndDate: DateTime.fromISO('2025-01-15T15:30:00Z'),
      isAvailable: false,
      visibilityScope: 'public',
    })

    const specificDate = DateTime.fromISO('2025-01-15')
    const specificTimeRange = specificSlot.getTimeRangeForDate(specificDate)

    assert.exists(specificTimeRange, 'Should return time range for specific slot')
    assert.equal(specificTimeRange!.start.hour, 14, 'Should have correct specific start hour')
    assert.equal(specificTimeRange!.end.hour, 15, 'Should have correct specific end hour')
    assert.equal(specificTimeRange!.end.minute, 30, 'Should have correct specific end minute')
  })

  /**
   * Test visibility check method
   */
  test('isVisibleToUser works correctly', async ({ assert }) => {
    // Public slot
    const publicSlot = await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'Public Hours',
      dayOfWeek: 1,
      startTime: '09:00',
      endTime: '17:00',
      isAvailable: true,
      visibilityScope: 'public',
    })

    const mockContext = {
      isContact: false,
      groupIds: [],
      contactListIds: [],
      fromCache: false,
    }

    assert.isTrue(publicSlot.isVisibleToUser('any-user-id', mockContext), 'Public slot should be visible to anyone')

    // Contact-only slot
    const contactSlot = await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'Contact Hours',
      dayOfWeek: 1,
      startTime: '09:00',
      endTime: '17:00',
      isAvailable: true,
      visibilityScope: 'all_contacts',
    })

    assert.isFalse(contactSlot.isVisibleToUser('any-user-id', mockContext), 'Contact slot should not be visible to non-contacts')

    const contactContext = { ...mockContext, isContact: true }
    assert.isTrue(contactSlot.isVisibleToUser('any-user-id', contactContext), 'Contact slot should be visible to contacts')

    // Group-specific slot
    const groupSlot = await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'Group Hours',
      dayOfWeek: 1,
      startTime: '09:00',
      endTime: '17:00',
      isAvailable: true,
      visibilityScope: 'specific_group',
      visibilityTargetGroupId: 'group-123',
    })

    assert.isFalse(groupSlot.isVisibleToUser('any-user-id', mockContext), 'Group slot should not be visible to non-members')

    const groupContext = { ...mockContext, groupIds: ['group-123'] }
    assert.isTrue(groupSlot.isVisibleToUser('any-user-id', groupContext), 'Group slot should be visible to group members')
  })
})

test.group('UserAvailabilitySlotService', (group) => {
  let service: UserAvailabilitySlotService
  let user: User

  group.setup(async () => {
    service = new UserAvailabilitySlotService()
  })

  group.each.setup(async () => {
    user = await User.create({
      email: '<EMAIL>',
      password: 'password123',
    })
  })

  /**
   * Test slot creation validation
   */
  test('createSlot validates input correctly', async ({ assert }) => {
    // Valid recurring slot
    const validRecurringData = {
      title: 'Work Hours',
      dayOfWeek: 1,
      startTime: '09:00',
      endTime: '17:00',
      isAvailable: true,
      visibilityScope: 'public' as AvailabilityScopeType,
    }

    const recurringSlot = await service.createSlot(user.id, validRecurringData)
    assert.equal(recurringSlot.title, 'Work Hours')
    assert.equal(recurringSlot.dayOfWeek, 1)
    assert.isTrue(recurringSlot.isRecurring)

    // Valid specific slot
    const validSpecificData = {
      title: 'Meeting',
      specificStartDate: '2025-01-15T14:00:00Z',
      specificEndDate: '2025-01-15T15:00:00Z',
      isAvailable: false,
      visibilityScope: 'public' as AvailabilityScopeType,
    }

    const specificSlot = await service.createSlot(user.id, validSpecificData)
    assert.equal(specificSlot.title, 'Meeting')
    assert.isTrue(specificSlot.isSpecific)

    // Invalid: both recurring and specific data
    const invalidData = {
      title: 'Invalid Slot',
      dayOfWeek: 1,
      startTime: '09:00',
      endTime: '17:00',
      specificStartDate: '2025-01-15T14:00:00Z',
      specificEndDate: '2025-01-15T15:00:00Z',
      isAvailable: true,
      visibilityScope: 'public' as AvailabilityScopeType,
    }

    await assert.rejects(
      () => service.createSlot(user.id, invalidData),
      'Cannot specify both recurring and specific availability in the same slot'
    )

    // Invalid: neither recurring nor specific data
    const emptyData = {
      title: 'Empty Slot',
      isAvailable: true,
      visibilityScope: 'public' as AvailabilityScopeType,
    }

    await assert.rejects(
      () => service.createSlot(user.id, emptyData),
      'Must specify either recurring or specific availability'
    )
  })

  /**
   * Test time validation
   */
  test('createSlot validates time ordering', async ({ assert }) => {
    // Invalid: start time after end time
    const invalidTimeData = {
      title: 'Invalid Time',
      dayOfWeek: 1,
      startTime: '17:00',
      endTime: '09:00', // End before start
      isAvailable: true,
      visibilityScope: 'public' as AvailabilityScopeType,
    }

    await assert.rejects(
      () => service.createSlot(user.id, invalidTimeData),
      'Start time must be before end time'
    )

    // Invalid: specific start date after end date
    const invalidSpecificData = {
      title: 'Invalid Specific',
      specificStartDate: '2025-01-15T15:00:00Z',
      specificEndDate: '2025-01-15T14:00:00Z', // End before start
      isAvailable: true,
      visibilityScope: 'public' as AvailabilityScopeType,
    }

    await assert.rejects(
      () => service.createSlot(user.id, invalidSpecificData),
      'Specific start date must be before end date'
    )
  })

  /**
   * Test slot updating
   */
  test('updateSlot works correctly', async ({ assert }) => {
    // Create initial slot
    const slot = await service.createSlot(user.id, {
      title: 'Original Title',
      dayOfWeek: 1,
      startTime: '09:00',
      endTime: '17:00',
      isAvailable: true,
      visibilityScope: 'public' as AvailabilityScopeType,
    })

    // Update slot
    const updatedSlot = await service.updateSlot(slot.id, user.id, {
      title: 'Updated Title',
      startTime: '10:00',
    })

    assert.equal(updatedSlot.title, 'Updated Title')
    assert.equal(updatedSlot.startTime, '10:00')
    assert.equal(updatedSlot.endTime, '17:00') // Should remain unchanged
    assert.equal(updatedSlot.dayOfWeek, 1) // Should remain unchanged
  })

  /**
   * Test slot deletion
   */
  test('deleteSlot works correctly', async ({ assert }) => {
    // Create slot
    const slot = await service.createSlot(user.id, {
      title: 'To Delete',
      dayOfWeek: 1,
      startTime: '09:00',
      endTime: '17:00',
      isAvailable: true,
      visibilityScope: 'public' as AvailabilityScopeType,
    })

    // Delete slot
    await service.deleteSlot(slot.id, user.id)

    // Verify slot is soft deleted
    await assert.rejects(
      () => service.getSlotById(slot.id, user.id),
      'Availability slot not found'
    )

    // Verify slot still exists in database but with deletedAt set
    const deletedSlot = await UserAvailabilitySlot.query()
      .where('id', slot.id)
      .withTrashed()
      .first()

    assert.exists(deletedSlot)
    assert.exists(deletedSlot!.deletedAt)
  })

  /**
   * Test getUserSlots with filtering
   */
  test('getUserSlots filtering works correctly', async ({ assert }) => {
    // Create different types of slots
    await service.createSlot(user.id, {
      title: 'Recurring Available',
      dayOfWeek: 1,
      startTime: '09:00',
      endTime: '17:00',
      isAvailable: true,
      visibilityScope: 'public' as AvailabilityScopeType,
    })

    await service.createSlot(user.id, {
      title: 'Recurring Unavailable',
      dayOfWeek: 2,
      startTime: '09:00',
      endTime: '17:00',
      isAvailable: false,
      visibilityScope: 'public' as AvailabilityScopeType,
    })

    await service.createSlot(user.id, {
      title: 'Specific Available',
      specificStartDate: '2025-01-15T09:00:00Z',
      specificEndDate: '2025-01-15T17:00:00Z',
      isAvailable: true,
      visibilityScope: 'all_contacts' as AvailabilityScopeType,
    })

    // Test filtering by slot type
    const recurringSlots = await service.getUserSlots(user.id, { slotType: 'recurring' })
    assert.equal(recurringSlots.length, 2, 'Should return 2 recurring slots')

    const specificSlots = await service.getUserSlots(user.id, { slotType: 'specific' })
    assert.equal(specificSlots.length, 1, 'Should return 1 specific slot')

    // Test filtering by availability
    const availableSlots = await service.getUserSlots(user.id, { isAvailable: true })
    assert.equal(availableSlots.length, 2, 'Should return 2 available slots')

    const unavailableSlots = await service.getUserSlots(user.id, { isAvailable: false })
    assert.equal(unavailableSlots.length, 1, 'Should return 1 unavailable slot')

    // Test filtering by visibility scope
    const publicSlots = await service.getUserSlots(user.id, { visibilityScope: 'public' })
    assert.equal(publicSlots.length, 2, 'Should return 2 public slots')

    const contactSlots = await service.getUserSlots(user.id, { visibilityScope: 'all_contacts' })
    assert.equal(contactSlots.length, 1, 'Should return 1 contact slot')

    // Test filtering by day of week
    const mondaySlots = await service.getUserSlots(user.id, { dayOfWeek: 1 })
    assert.equal(mondaySlots.length, 1, 'Should return 1 Monday slot')
  })
})
