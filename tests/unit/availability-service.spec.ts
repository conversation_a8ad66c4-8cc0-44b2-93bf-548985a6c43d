import { test } from '@japa/runner'
import { DateTime } from 'luxon'
import User from '#features/user/user_model'
import Task from '#features/task/task_model'
import Event from '#features/event/event_model'
import AvailabilityService from '#features/availability/availability_service'
import PrivacyPolicyTemplate from '#features/privacy-policy/template/privacy_policy_template_model'
import PolicyTemplateRule from '#features/privacy-policy/rule/policy_template_rule_model'
import PrivacyAssignment from '#models/privacy_assignment'
import UserContact from '#features/contact/user_contact_model'
import UserGroup from '#features/group/user_group_model'
import GroupMember from '#features/group/members/group_member_model'
import { ActivityDetailVisibilityLevel } from '#types/policy_template_types'

/**
 * Unit Tests: AvailabilityService
 * 
 * These tests validate the core business logic of the availability service,
 * focusing on privacy rule evaluation and activity transformation.
 */

test.group('AvailabilityService Privacy Rule Evaluation', (group) => {
  let service: AvailabilityService
  let user1: User, user2: User
  let testDate: DateTime

  group.setup(async () => {
    service = new AvailabilityService()
    testDate = DateTime.fromISO('2025-01-15T10:00:00Z')
  })

  group.each.setup(async () => {
    user1 = await User.create({ email: '<EMAIL>', password: 'password' })
    user2 = await User.create({ email: '<EMAIL>', password: 'password' })
  })

  /**
   * Test activity visibility transformation
   */
  test('transforms activities correctly based on visibility level', async ({ assert }) => {
    const mockActivity = {
      id: 'task-123',
      activityType: 'task',
      title: 'Important Meeting',
      description: 'Confidential project discussion',
      location: 'Conference Room A',
      startDate: testDate.toJSDate(),
      endDate: testDate.plus({ hours: 1 }).toJSDate(),
      metadata: { priority: 'high' },
    }

    const mockContext = {
      isContact: false,
      groupIds: [],
      contactListIds: [],
      fromCache: false,
    }

    // Test HIDDEN level
    const hiddenResult = service['evaluateActivityVisibility'](
      { ...mockActivity, privacyAssignments: [] },
      mockContext
    )
    assert.isFalse(hiddenResult.isVisible, 'Hidden activity should not be visible')

    // Test BUSY_ONLY level
    const busyOnlyActivity = {
      ...mockActivity,
      privacyAssignments: [{
        template: {
          rules: [{
            viewerScopeType: 'public',
            detailVisibility: ActivityDetailVisibilityLevel.BUSY_ONLY,
            blocksScheduling: true,
            priority: 1,
          }],
        },
      }],
    }

    const busyResult = service['evaluateActivityVisibility'](busyOnlyActivity, mockContext)
    assert.isTrue(busyResult.isVisible, 'Busy activity should be visible')
    assert.equal(busyResult.detailLevel, ActivityDetailVisibilityLevel.BUSY_ONLY)

    const transformedBusy = service['transformActivityByVisibilityLevel'](mockActivity, busyResult)
    assert.equal(transformedBusy.title, 'Busy', 'Should show default busy message')
    assert.isNull(transformedBusy.description, 'Should hide description')
    assert.isNull(transformedBusy.location, 'Should hide location')

    // Test TITLE_ONLY level
    const titleOnlyActivity = {
      ...mockActivity,
      privacyAssignments: [{
        template: {
          rules: [{
            viewerScopeType: 'public',
            detailVisibility: ActivityDetailVisibilityLevel.TITLE_ONLY,
            blocksScheduling: false,
            priority: 1,
          }],
        },
      }],
    }

    const titleResult = service['evaluateActivityVisibility'](titleOnlyActivity, mockContext)
    const transformedTitle = service['transformActivityByVisibilityLevel'](mockActivity, titleResult)
    assert.equal(transformedTitle.title, 'Important Meeting', 'Should show title')
    assert.isNull(transformedTitle.description, 'Should hide description')
    assert.isNull(transformedTitle.location, 'Should hide location')

    // Test FULL_DETAILS level
    const fullDetailsActivity = {
      ...mockActivity,
      privacyAssignments: [{
        template: {
          rules: [{
            viewerScopeType: 'public',
            detailVisibility: ActivityDetailVisibilityLevel.FULL_DETAILS,
            blocksScheduling: false,
            priority: 1,
          }],
        },
      }],
    }

    const fullResult = service['evaluateActivityVisibility'](fullDetailsActivity, mockContext)
    const transformedFull = service['transformActivityByVisibilityLevel'](mockActivity, fullResult)
    assert.equal(transformedFull.title, 'Important Meeting', 'Should show title')
    assert.equal(transformedFull.description, 'Confidential project discussion', 'Should show description')
    assert.equal(transformedFull.location, 'Conference Room A', 'Should show location')
    assert.deepEqual(transformedFull.metadata, { priority: 'high' }, 'Should show metadata')
  })

  /**
   * Test rule matching logic
   */
  test('rule matching works correctly for different viewer scope types', async ({ assert }) => {
    const mockRule = {
      viewerScopeType: 'allContacts',
      detailVisibility: ActivityDetailVisibilityLevel.FULL_DETAILS,
      blocksScheduling: false,
      priority: 1,
    }

    // Test contact matching
    const contactContext = {
      isContact: true,
      groupIds: [],
      contactListIds: [],
      fromCache: false,
    }
    assert.isTrue(
      service['doesRuleApplyToRequester'](mockRule, contactContext),
      'Rule should apply to contacts'
    )

    const nonContactContext = {
      isContact: false,
      groupIds: [],
      contactListIds: [],
      fromCache: false,
    }
    assert.isFalse(
      service['doesRuleApplyToRequester'](mockRule, nonContactContext),
      'Rule should not apply to non-contacts'
    )

    // Test group matching
    const groupRule = {
      ...mockRule,
      viewerScopeType: 'specificGroup',
      viewerTargetGroupId: 'group-123',
    }

    const groupContext = {
      isContact: false,
      groupIds: ['group-123', 'group-456'],
      contactListIds: [],
      fromCache: false,
    }
    assert.isTrue(
      service['doesRuleApplyToRequester'](groupRule, groupContext),
      'Rule should apply to group members'
    )

    const nonGroupContext = {
      isContact: false,
      groupIds: ['group-456'],
      contactListIds: [],
      fromCache: false,
    }
    assert.isFalse(
      service['doesRuleApplyToRequester'](groupRule, nonGroupContext),
      'Rule should not apply to non-group members'
    )

    // Test contact list matching
    const contactListRule = {
      ...mockRule,
      viewerScopeType: 'specificContactList',
      viewerTargetContactListId: 'list-123',
    }

    const contactListContext = {
      isContact: false,
      groupIds: [],
      contactListIds: ['list-123', 'list-456'],
      fromCache: false,
    }
    assert.isTrue(
      service['doesRuleApplyToRequester'](contactListRule, contactListContext),
      'Rule should apply to contact list members'
    )

    // Test public matching
    const publicRule = {
      ...mockRule,
      viewerScopeType: 'public',
    }
    assert.isTrue(
      service['doesRuleApplyToRequester'](publicRule, nonContactContext),
      'Public rule should apply to anyone'
    )
  })

  /**
   * Test custom message handling
   */
  test('custom messages are applied correctly', async ({ assert }) => {
    const mockActivity = {
      id: 'task-123',
      activityType: 'task',
      title: 'Meeting',
      description: 'Description',
      startDate: testDate.toJSDate(),
      endDate: testDate.plus({ hours: 1 }).toJSDate(),
      privacyAssignments: [{
        template: {
          rules: [{
            viewerScopeType: 'public',
            detailVisibility: ActivityDetailVisibilityLevel.BUSY_ONLY,
            blocksScheduling: true,
            customMessage: 'In a client meeting',
            priority: 1,
          }],
        },
      }],
    }

    const mockContext = {
      isContact: false,
      groupIds: [],
      contactListIds: [],
      fromCache: false,
    }

    const result = service['evaluateActivityVisibility'](mockActivity, mockContext)
    const transformed = service['transformActivityByVisibilityLevel'](mockActivity, result)

    assert.equal(transformed.title, 'In a client meeting', 'Should use custom message')
  })

  /**
   * Test override handling
   */
  test('privacy assignment overrides work correctly', async ({ assert }) => {
    const mockActivity = {
      id: 'task-123',
      activityType: 'task',
      title: 'Meeting',
      description: 'Description',
      startDate: testDate.toJSDate(),
      endDate: testDate.plus({ hours: 1 }).toJSDate(),
      privacyAssignments: [{
        template: {
          rules: [{
            viewerScopeType: 'public',
            detailVisibility: ActivityDetailVisibilityLevel.TITLE_ONLY,
            blocksScheduling: false,
            priority: 1,
          }],
        },
        // Override template settings
        overrideDetailVisibility: ActivityDetailVisibilityLevel.BUSY_ONLY,
        overrideBlocksScheduling: true,
        overrideCustomMessage: 'Override message',
      }],
    }

    const mockContext = {
      isContact: false,
      groupIds: [],
      contactListIds: [],
      fromCache: false,
    }

    const result = service['evaluateActivityVisibility'](mockActivity, mockContext)
    assert.equal(result.detailLevel, ActivityDetailVisibilityLevel.BUSY_ONLY, 'Should use override visibility')
    assert.isTrue(result.blocksScheduling, 'Should use override scheduling')
    assert.equal(result.customMessage, 'Override message', 'Should use override message')
  })

  /**
   * Test default behavior when no template exists
   */
  test('applies default behavior when no privacy template exists', async ({ assert }) => {
    const mockActivity = {
      id: 'task-123',
      activityType: 'task',
      title: 'Unprotected Meeting',
      description: 'No privacy template',
      startDate: testDate.toJSDate(),
      endDate: testDate.plus({ hours: 1 }).toJSDate(),
      privacyAssignments: [], // No privacy assignment
    }

    const mockContext = {
      isContact: false,
      groupIds: [],
      contactListIds: [],
      fromCache: false,
    }

    const result = service['evaluateActivityVisibility'](mockActivity, mockContext)
    assert.isTrue(result.isVisible, 'Activity should be visible with default behavior')
    assert.equal(result.detailLevel, ActivityDetailVisibilityLevel.BUSY_ONLY, 'Should default to busy only')
    assert.isTrue(result.blocksScheduling, 'Should default to blocking scheduling')

    const transformed = service['transformActivityByVisibilityLevel'](mockActivity, result)
    assert.equal(transformed.title, 'Busy', 'Should show default busy message')
  })

  /**
   * Test template defaults when no rules match
   */
  test('uses template defaults when no rules match viewer', async ({ assert }) => {
    const mockActivity = {
      id: 'task-123',
      activityType: 'task',
      title: 'Restricted Meeting',
      description: 'Only for specific people',
      startDate: testDate.toJSDate(),
      endDate: testDate.plus({ hours: 1 }).toJSDate(),
      privacyAssignments: [{
        template: {
          defaultDetailVisibility: ActivityDetailVisibilityLevel.TITLE_ONLY,
          blocksScheduling: false,
          defaultCustomMessage: 'Template default message',
          rules: [{
            viewerScopeType: 'specificContact',
            viewerTargetUserId: 'different-user-id',
            detailVisibility: ActivityDetailVisibilityLevel.FULL_DETAILS,
            blocksScheduling: false,
            priority: 1,
          }],
        },
      }],
    }

    const mockContext = {
      isContact: false,
      groupIds: [],
      contactListIds: [],
      fromCache: false,
    }

    const result = service['evaluateActivityVisibility'](mockActivity, mockContext)
    assert.isTrue(result.isVisible, 'Activity should be visible')
    assert.equal(result.detailLevel, ActivityDetailVisibilityLevel.TITLE_ONLY, 'Should use template default visibility')
    assert.isFalse(result.blocksScheduling, 'Should use template default scheduling')

    const transformed = service['transformActivityByVisibilityLevel'](mockActivity, result)
    assert.equal(transformed.title, 'Restricted Meeting', 'Should show title per template default')
    assert.isNull(transformed.description, 'Should hide description per template default')
  })

  /**
   * Test in-memory processing of multiple activities
   */
  test('processes multiple activities correctly in memory', async ({ assert }) => {
    const activities = [
      {
        id: 'task-1',
        activityType: 'task',
        title: 'Public Meeting',
        startDate: testDate.toJSDate(),
        endDate: testDate.plus({ hours: 1 }).toJSDate(),
        privacyAssignments: [{
          template: {
            rules: [{
              viewerScopeType: 'public',
              detailVisibility: ActivityDetailVisibilityLevel.FULL_DETAILS,
              blocksScheduling: false,
              priority: 1,
            }],
          },
        }],
      },
      {
        id: 'task-2',
        activityType: 'task',
        title: 'Hidden Meeting',
        startDate: testDate.plus({ hours: 2 }).toJSDate(),
        endDate: testDate.plus({ hours: 3 }).toJSDate(),
        privacyAssignments: [{
          template: {
            rules: [{
              viewerScopeType: 'specificContact',
              viewerTargetUserId: 'different-user',
              detailVisibility: ActivityDetailVisibilityLevel.HIDDEN,
              blocksScheduling: true,
              priority: 1,
            }],
          },
        }],
      },
      {
        id: 'task-3',
        activityType: 'task',
        title: 'Busy Meeting',
        startDate: testDate.plus({ hours: 4 }).toJSDate(),
        endDate: testDate.plus({ hours: 5 }).toJSDate(),
        privacyAssignments: [{
          template: {
            rules: [{
              viewerScopeType: 'public',
              detailVisibility: ActivityDetailVisibilityLevel.BUSY_ONLY,
              blocksScheduling: true,
              priority: 1,
            }],
          },
        }],
      },
    ]

    const mockContext = {
      isContact: false,
      groupIds: [],
      contactListIds: [],
      fromCache: false,
    }

    const processed = service['processActivitiesInMemory'](activities, mockContext)

    assert.equal(processed.length, 2, 'Should return 2 visible activities (hidden one filtered out)')
    
    const publicActivity = processed.find(a => a.id === 'task-1')
    assert.exists(publicActivity, 'Public activity should be included')
    assert.equal(publicActivity.title, 'Public Meeting', 'Public activity should show full details')

    const busyActivity = processed.find(a => a.id === 'task-3')
    assert.exists(busyActivity, 'Busy activity should be included')
    assert.equal(busyActivity.title, 'Busy', 'Busy activity should show busy message')

    const hiddenActivity = processed.find(a => a.id === 'task-2')
    assert.notExists(hiddenActivity, 'Hidden activity should not be included')
  })
})
