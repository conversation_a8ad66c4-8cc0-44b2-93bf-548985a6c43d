import { test } from '@japa/runner'
import { DateTime } from 'luxon'
import redis from '@adonisjs/redis/services/main'
import User from '#features/user/user_model'
import Task from '#features/task/task_model'
import Event from '#features/event/event_model'
import UserAvailabilitySlot from '#features/availability/user_availability_slot_model'
import AvailabilityService from '#features/availability/availability_service'
import UserContact from '#features/contact/user_contact_model'
import PrivacyPolicyTemplate from '#features/privacy-policy/template/privacy_policy_template_model'
import PolicyTemplateRule from '#features/privacy-policy/rule/policy_template_rule_model'
import PrivacyAssignment from '#models/privacy_assignment'
import { ActivityDetailVisibilityLevel } from '#types/policy_template_types'

/**
 * Performance Tests: Availability System
 * 
 * These tests validate that the availability system meets performance targets:
 * - Cache Hit: 2 queries, <50ms response time, >90% hit rate
 * - Cache Miss: 3 queries, <150ms response time
 * - Batch Requests: <500ms for 7-day availability queries
 */

test.group('Availability Performance Tests', (group) => {
  let availabilityService: AvailabilityService
  let user1: User, user2: User
  let testDate: DateTime

  group.setup(async () => {
    availabilityService = new AvailabilityService()
    testDate = DateTime.fromISO('2025-01-15T10:00:00Z')
  })

  group.each.setup(async () => {
    // Clear Redis cache before each test
    await redis.flushall()
    
    user1 = await User.create({ email: '<EMAIL>', password: 'password' })
    user2 = await User.create({ email: '<EMAIL>', password: 'password' })
  })

  group.each.teardown(async () => {
    await redis.flushall()
  })

  /**
   * Test cache hit performance target: <50ms response time
   */
  test('cache hit queries meet <50ms performance target', async ({ assert }) => {
    // Setup: Create test data
    await UserContact.create({
      userId: user1.id,
      contactUserId: user2.id,
      status: 'accepted',
    })

    const template = await PrivacyPolicyTemplate.create({
      userId: user1.id,
      name: 'Performance Test Template',
      blocksScheduling: false,
      defaultDetailVisibility: ActivityDetailVisibilityLevel.TITLE_ONLY,
    })

    await PolicyTemplateRule.create({
      userId: user1.id,
      templateId: template.id,
      viewerScopeType: 'allContacts',
      detailVisibility: ActivityDetailVisibilityLevel.FULL_DETAILS,
      blocksScheduling: false,
      priority: 1,
    })

    await Task.create({
      userId: user1.id,
      title: 'Performance Test Task',
      description: 'Testing cache performance',
      startDate: testDate.toJSDate(),
      endDate: testDate.plus({ hours: 1 }).toJSDate(),
    })

    await UserAvailabilitySlot.create({
      userId: user1.id,
      title: 'Work Hours',
      dayOfWeek: testDate.weekday,
      startTime: '09:00',
      endTime: '17:00',
      isAvailable: true,
      visibilityScope: 'public',
    })

    // First call to populate cache
    await availabilityService.getAvailabilityForDay(user2.id, user1.id, testDate)

    // Measure cache hit performance
    const startTime = process.hrtime.bigint()
    const result = await availabilityService.getAvailabilityForDay(user2.id, user1.id, testDate)
    const endTime = process.hrtime.bigint()

    const responseTimeMs = Number(endTime - startTime) / 1_000_000

    assert.isTrue(result.requesterContext.fromCache, 'Should be a cache hit')
    assert.isBelow(responseTimeMs, 50, `Cache hit should be <50ms, got ${responseTimeMs.toFixed(2)}ms`)
    
    console.log(`Cache hit response time: ${responseTimeMs.toFixed(2)}ms`)
  })

  /**
   * Test cache miss performance target: <150ms response time
   */
  test('cache miss queries meet <150ms performance target', async ({ assert }) => {
    // Setup: Create test data
    await UserContact.create({
      userId: user1.id,
      contactUserId: user2.id,
      status: 'accepted',
    })

    const template = await PrivacyPolicyTemplate.create({
      userId: user1.id,
      name: 'Performance Test Template',
      blocksScheduling: false,
      defaultDetailVisibility: ActivityDetailVisibilityLevel.TITLE_ONLY,
    })

    await PolicyTemplateRule.create({
      userId: user1.id,
      templateId: template.id,
      viewerScopeType: 'allContacts',
      detailVisibility: ActivityDetailVisibilityLevel.FULL_DETAILS,
      blocksScheduling: false,
      priority: 1,
    })

    // Create multiple activities to test query performance
    for (let i = 0; i < 10; i++) {
      const task = await Task.create({
        userId: user1.id,
        title: `Performance Test Task ${i}`,
        description: `Testing cache miss performance ${i}`,
        startDate: testDate.plus({ hours: i }).toJSDate(),
        endDate: testDate.plus({ hours: i + 1 }).toJSDate(),
      })

      await PrivacyAssignment.create({
        taskId: task.id,
        templateId: template.id,
      })
    }

    // Create multiple availability slots
    for (let i = 0; i < 5; i++) {
      await UserAvailabilitySlot.create({
        userId: user1.id,
        title: `Availability Slot ${i}`,
        dayOfWeek: testDate.weekday,
        startTime: `${9 + i}:00`,
        endTime: `${10 + i}:00`,
        isAvailable: i % 2 === 0,
        visibilityScope: 'public',
      })
    }

    // Measure cache miss performance (first call)
    const startTime = process.hrtime.bigint()
    const result = await availabilityService.getAvailabilityForDay(user2.id, user1.id, testDate)
    const endTime = process.hrtime.bigint()

    const responseTimeMs = Number(endTime - startTime) / 1_000_000

    assert.isFalse(result.requesterContext.fromCache, 'Should be a cache miss')
    assert.isBelow(responseTimeMs, 150, `Cache miss should be <150ms, got ${responseTimeMs.toFixed(2)}ms`)
    assert.equal(result.activities.length, 10, 'Should return all activities')
    assert.equal(result.generalAvailability.length, 5, 'Should return all availability slots')
    
    console.log(`Cache miss response time: ${responseTimeMs.toFixed(2)}ms`)
  })

  /**
   * Test batch query performance target: <500ms for 7-day range
   */
  test('7-day batch queries meet <500ms performance target', async ({ assert }) => {
    // Setup: Create test data across 7 days
    await UserContact.create({
      userId: user1.id,
      contactUserId: user2.id,
      status: 'accepted',
    })

    const template = await PrivacyPolicyTemplate.create({
      userId: user1.id,
      name: 'Batch Performance Template',
      blocksScheduling: false,
      defaultDetailVisibility: ActivityDetailVisibilityLevel.TITLE_ONLY,
    })

    await PolicyTemplateRule.create({
      userId: user1.id,
      templateId: template.id,
      viewerScopeType: 'allContacts',
      detailVisibility: ActivityDetailVisibilityLevel.FULL_DETAILS,
      blocksScheduling: false,
      priority: 1,
    })

    const startDate = testDate.startOf('day')
    const endDate = startDate.plus({ days: 6 }) // 7 days total

    // Create activities across all 7 days (5 activities per day)
    for (let day = 0; day < 7; day++) {
      const currentDate = startDate.plus({ days: day })
      
      for (let hour = 0; hour < 5; hour++) {
        const task = await Task.create({
          userId: user1.id,
          title: `Day ${day + 1} Task ${hour + 1}`,
          description: `Batch performance test`,
          startDate: currentDate.plus({ hours: 9 + hour }).toJSDate(),
          endDate: currentDate.plus({ hours: 10 + hour }).toJSDate(),
        })

        await PrivacyAssignment.create({
          taskId: task.id,
          templateId: template.id,
        })
      }

      // Create availability slots for each day
      await UserAvailabilitySlot.create({
        userId: user1.id,
        title: `Day ${day + 1} Work Hours`,
        dayOfWeek: currentDate.weekday,
        startTime: '09:00',
        endTime: '17:00',
        isAvailable: true,
        visibilityScope: 'public',
      })
    }

    // Measure batch query performance
    const startTime = process.hrtime.bigint()
    const results = await availabilityService.getAvailabilityForDateRange(
      user2.id,
      user1.id,
      startDate,
      endDate
    )
    const endTime = process.hrtime.bigint()

    const responseTimeMs = Number(endTime - startTime) / 1_000_000

    assert.equal(results.length, 7, 'Should return 7 days of data')
    assert.isBelow(responseTimeMs, 500, `7-day batch query should be <500ms, got ${responseTimeMs.toFixed(2)}ms`)

    // Verify data integrity
    let totalActivities = 0
    let totalAvailabilitySlots = 0
    
    results.forEach((dayResult, index) => {
      assert.equal(dayResult.activities.length, 5, `Day ${index + 1} should have 5 activities`)
      assert.equal(dayResult.generalAvailability.length, 1, `Day ${index + 1} should have 1 availability slot`)
      totalActivities += dayResult.activities.length
      totalAvailabilitySlots += dayResult.generalAvailability.length
    })

    assert.equal(totalActivities, 35, 'Should have 35 total activities (5 per day × 7 days)')
    assert.equal(totalAvailabilitySlots, 7, 'Should have 7 total availability slots (1 per day)')
    
    console.log(`7-day batch query response time: ${responseTimeMs.toFixed(2)}ms`)
  })

  /**
   * Test cache hit ratio target: >90% hit rate
   */
  test('cache achieves >90% hit rate under repeated queries', async ({ assert }) => {
    // Setup: Create test data
    await UserContact.create({
      userId: user1.id,
      contactUserId: user2.id,
      status: 'accepted',
    })

    await Task.create({
      userId: user1.id,
      title: 'Cache Hit Test Task',
      startDate: testDate.toJSDate(),
      endDate: testDate.plus({ hours: 1 }).toJSDate(),
    })

    // Make 100 requests (first will be cache miss, rest should be cache hits)
    let cacheHits = 0
    const totalRequests = 100

    for (let i = 0; i < totalRequests; i++) {
      const result = await availabilityService.getAvailabilityForDay(user2.id, user1.id, testDate)
      if (result.requesterContext.fromCache) {
        cacheHits++
      }
    }

    const hitRate = (cacheHits / totalRequests) * 100

    assert.isAbove(hitRate, 90, `Cache hit rate should be >90%, got ${hitRate.toFixed(1)}%`)
    
    console.log(`Cache hit rate: ${hitRate.toFixed(1)}% (${cacheHits}/${totalRequests})`)
  })

  /**
   * Test concurrent user performance
   */
  test('handles concurrent users efficiently', async ({ assert }) => {
    // Setup: Create multiple users and relationships
    const users = []
    for (let i = 0; i < 10; i++) {
      const user = await User.create({
        email: `user${i}@test.com`,
        password: 'password',
      })
      users.push(user)

      // Create contact relationship with user1
      await UserContact.create({
        userId: user1.id,
        contactUserId: user.id,
        status: 'accepted',
      })
    }

    // Create activities for user1
    for (let i = 0; i < 20; i++) {
      await Task.create({
        userId: user1.id,
        title: `Concurrent Test Task ${i}`,
        startDate: testDate.plus({ minutes: i * 30 }).toJSDate(),
        endDate: testDate.plus({ minutes: i * 30 + 30 }).toJSDate(),
      })
    }

    // Simulate 10 concurrent users querying user1's availability
    const startTime = process.hrtime.bigint()
    
    const concurrentPromises = users.map(user =>
      availabilityService.getAvailabilityForDay(user.id, user1.id, testDate)
    )

    const results = await Promise.all(concurrentPromises)
    
    const endTime = process.hrtime.bigint()
    const totalTimeMs = Number(endTime - startTime) / 1_000_000

    // All requests should complete successfully
    assert.equal(results.length, 10, 'All concurrent requests should complete')
    
    // Average time per request should be reasonable
    const avgTimePerRequest = totalTimeMs / 10
    assert.isBelow(avgTimePerRequest, 100, `Average time per concurrent request should be <100ms, got ${avgTimePerRequest.toFixed(2)}ms`)

    // All results should be consistent
    results.forEach((result, index) => {
      assert.equal(result.activities.length, 20, `User ${index} should see all activities`)
      assert.equal(result.targetUserId, user1.id, `User ${index} should query correct target`)
    })

    console.log(`Concurrent users: 10 requests completed in ${totalTimeMs.toFixed(2)}ms (avg: ${avgTimePerRequest.toFixed(2)}ms per request)`)
  })

  /**
   * Test memory usage under load
   */
  test('maintains reasonable memory usage under load', async ({ assert }) => {
    // Setup: Create large dataset
    await UserContact.create({
      userId: user1.id,
      contactUserId: user2.id,
      status: 'accepted',
    })

    const template = await PrivacyPolicyTemplate.create({
      userId: user1.id,
      name: 'Memory Test Template',
      blocksScheduling: false,
      defaultDetailVisibility: ActivityDetailVisibilityLevel.FULL_DETAILS,
    })

    // Create 100 activities
    for (let i = 0; i < 100; i++) {
      const task = await Task.create({
        userId: user1.id,
        title: `Memory Test Task ${i}`,
        description: `Large description for memory testing ${i}`.repeat(10),
        startDate: testDate.plus({ minutes: i * 15 }).toJSDate(),
        endDate: testDate.plus({ minutes: i * 15 + 15 }).toJSDate(),
      })

      await PrivacyAssignment.create({
        taskId: task.id,
        templateId: template.id,
      })
    }

    // Get initial cache stats
    const initialStats = await availabilityService.getCacheStats()

    // Make multiple queries to populate cache
    for (let i = 0; i < 50; i++) {
      await availabilityService.getAvailabilityForDay(user2.id, user1.id, testDate)
    }

    // Get final cache stats
    const finalStats = await availabilityService.getCacheStats()

    // Cache should have grown but not excessively
    assert.isAbove(finalStats.relationshipCacheSize, initialStats.relationshipCacheSize, 'Relationship cache should grow')
    assert.isAbove(finalStats.activityCacheSize, initialStats.activityCacheSize, 'Activity cache should grow')

    // Memory usage should be reported
    assert.exists(finalStats.totalMemoryUsage, 'Should report memory usage')
    
    console.log('Cache stats after load test:', finalStats)
  })

  /**
   * Test query optimization with complex privacy rules
   */
  test('maintains performance with complex privacy rule evaluation', async ({ assert }) => {
    // Setup: Create complex privacy template with multiple rules
    await UserContact.create({
      userId: user1.id,
      contactUserId: user2.id,
      status: 'accepted',
    })

    const template = await PrivacyPolicyTemplate.create({
      userId: user1.id,
      name: 'Complex Privacy Template',
      blocksScheduling: false,
      defaultDetailVisibility: ActivityDetailVisibilityLevel.BUSY_ONLY,
    })

    // Create 10 privacy rules with different priorities
    for (let i = 0; i < 10; i++) {
      await PolicyTemplateRule.create({
        userId: user1.id,
        templateId: template.id,
        viewerScopeType: 'allContacts',
        detailVisibility: i % 3 === 0 ? ActivityDetailVisibilityLevel.FULL_DETAILS : 
                         i % 3 === 1 ? ActivityDetailVisibilityLevel.TITLE_ONLY : 
                         ActivityDetailVisibilityLevel.BUSY_ONLY,
        blocksScheduling: i % 2 === 0,
        priority: i + 1,
        customMessage: `Rule ${i} message`,
      })
    }

    // Create 50 activities with complex privacy assignments
    for (let i = 0; i < 50; i++) {
      const task = await Task.create({
        userId: user1.id,
        title: `Complex Privacy Task ${i}`,
        description: `Complex privacy evaluation test ${i}`,
        startDate: testDate.plus({ minutes: i * 10 }).toJSDate(),
        endDate: testDate.plus({ minutes: i * 10 + 10 }).toJSDate(),
      })

      await PrivacyAssignment.create({
        taskId: task.id,
        templateId: template.id,
        // Add some overrides for complexity
        overrideDetailVisibility: i % 5 === 0 ? ActivityDetailVisibilityLevel.HIDDEN : undefined,
        overrideBlocksScheduling: i % 7 === 0 ? true : undefined,
        overrideCustomMessage: i % 3 === 0 ? `Override message ${i}` : undefined,
      })
    }

    // Measure performance with complex rule evaluation
    const startTime = process.hrtime.bigint()
    const result = await availabilityService.getAvailabilityForDay(user2.id, user1.id, testDate)
    const endTime = process.hrtime.bigint()

    const responseTimeMs = Number(endTime - startTime) / 1_000_000

    // Should still meet performance targets despite complexity
    assert.isBelow(responseTimeMs, 200, `Complex privacy evaluation should be <200ms, got ${responseTimeMs.toFixed(2)}ms`)
    
    // Should correctly apply privacy rules (some activities might be hidden)
    assert.isBelow(result.activities.length, 50, 'Some activities should be hidden by privacy rules')
    assert.isAbove(result.activities.length, 0, 'Some activities should be visible')

    console.log(`Complex privacy evaluation time: ${responseTimeMs.toFixed(2)}ms for ${result.activities.length} visible activities`)
  })
})
