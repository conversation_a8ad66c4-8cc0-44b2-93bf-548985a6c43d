import { test } from '@japa/runner'
import { DateTime } from 'luxon'
import User from '#features/user/user_model'
import UserAvailabilitySlot from '#features/availability/user_availability_slot_model'
import UserAvailabilitySlotService from '#features/availability/user_availability_slot_service'
import AvailabilityService from '#features/availability/availability_service'
import Task from '#features/task/task_model'
import { AvailabilityScopeType } from '#types/policy_template_types'

/**
 * Edge Case Tests: Availability System
 * 
 * These tests validate handling of edge cases, invalid data,
 * and error scenarios to ensure system reliability.
 */

test.group('Availability Edge Cases', (group) => {
  let service: UserAvailabilitySlotService
  let availabilityService: AvailabilityService
  let user: User

  group.setup(async () => {
    service = new UserAvailabilitySlotService()
    availabilityService = new AvailabilityService()
  })

  group.each.setup(async () => {
    user = await User.create({
      email: '<EMAIL>',
      password: 'password123',
    })
  })

  /**
   * Test time zone edge cases
   */
  test('handles time zone edge cases correctly', async ({ assert }) => {
    // Test slot that spans midnight in different time zones
    const slot = await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'Late Night Availability',
      dayOfWeek: 1, // Monday
      startTime: '23:00',
      endTime: '23:59',
      isAvailable: true,
      visibilityScope: 'public',
    })

    // Test with different time zones
    const utcMonday = DateTime.fromISO('2025-01-13T12:00:00Z') // Monday UTC
    const pstMonday = DateTime.fromISO('2025-01-13T12:00:00-08:00') // Monday PST
    const jstMonday = DateTime.fromISO('2025-01-13T12:00:00+09:00') // Monday JST

    assert.isTrue(slot.isActiveOnDate(utcMonday), 'Should be active on UTC Monday')
    assert.isTrue(slot.isActiveOnDate(pstMonday), 'Should be active on PST Monday')
    assert.isTrue(slot.isActiveOnDate(jstMonday), 'Should be active on JST Monday')

    // Test time range calculation
    const timeRange = slot.getTimeRangeForDate(utcMonday)
    assert.exists(timeRange, 'Should return time range')
    assert.equal(timeRange!.start.hour, 23, 'Should have correct start hour')
    assert.equal(timeRange!.end.hour, 23, 'Should have correct end hour')
    assert.equal(timeRange!.end.minute, 59, 'Should have correct end minute')
  })

  /**
   * Test overlapping availability slots
   */
  test('handles overlapping availability slots correctly', async ({ assert }) => {
    // Create overlapping available and unavailable slots
    await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'Work Hours',
      dayOfWeek: 1, // Monday
      startTime: '09:00',
      endTime: '17:00',
      isAvailable: true,
      visibilityScope: 'public',
    })

    await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'Lunch Break',
      dayOfWeek: 1, // Monday
      startTime: '12:00',
      endTime: '13:00',
      isAvailable: false,
      visibilityScope: 'public',
    })

    await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'Meeting Block',
      dayOfWeek: 1, // Monday
      startTime: '14:00',
      endTime: '15:00',
      isAvailable: false,
      visibilityScope: 'public',
    })

    const monday = DateTime.fromISO('2025-01-13') // Monday
    const availability = await availabilityService.getAvailabilityForDay(
      user.id,
      user.id,
      monday
    )

    assert.equal(availability.generalAvailability.length, 3, 'Should return all overlapping slots')
    
    // Verify all slots are returned (system doesn't resolve conflicts automatically)
    const slotTitles = availability.generalAvailability.map(slot => slot.title)
    assert.include(slotTitles, 'Work Hours')
    assert.include(slotTitles, 'Lunch Break')
    assert.include(slotTitles, 'Meeting Block')
  })

  /**
   * Test recurring slot edge cases
   */
  test('handles recurring slot date range edge cases', async ({ assert }) => {
    // Slot that starts in the future
    const futureSlot = await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'Future Availability',
      dayOfWeek: 1, // Monday
      startTime: '09:00',
      endTime: '17:00',
      recurringStartDate: DateTime.fromISO('2025-06-01'), // Future start
      recurringEndDate: DateTime.fromISO('2025-12-31'),
      isAvailable: true,
      visibilityScope: 'public',
    })

    // Test before start date
    const earlyMonday = DateTime.fromISO('2025-01-13') // Monday before start
    assert.isFalse(futureSlot.isActiveOnDate(earlyMonday), 'Should not be active before start date')

    // Test after start date
    const laterMonday = DateTime.fromISO('2025-06-02') // Monday after start
    assert.isTrue(futureSlot.isActiveOnDate(laterMonday), 'Should be active after start date')

    // Slot that ended in the past
    const pastSlot = await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'Past Availability',
      dayOfWeek: 1, // Monday
      startTime: '09:00',
      endTime: '17:00',
      recurringStartDate: DateTime.fromISO('2024-01-01'),
      recurringEndDate: DateTime.fromISO('2024-12-31'), // Past end
      isAvailable: true,
      visibilityScope: 'public',
    })

    const currentMonday = DateTime.fromISO('2025-01-13') // Monday after end
    assert.isFalse(pastSlot.isActiveOnDate(currentMonday), 'Should not be active after end date')
  })

  /**
   * Test specific slot edge cases
   */
  test('handles specific slot date edge cases', async ({ assert }) => {
    // Multi-day specific slot
    const multiDaySlot = await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'Multi-day Unavailable',
      specificStartDate: DateTime.fromISO('2025-01-15T00:00:00Z'),
      specificEndDate: DateTime.fromISO('2025-01-17T23:59:59Z'), // 3 days
      isAvailable: false,
      visibilityScope: 'public',
    })

    // Test each day in the range
    const day1 = DateTime.fromISO('2025-01-15')
    const day2 = DateTime.fromISO('2025-01-16')
    const day3 = DateTime.fromISO('2025-01-17')
    const day4 = DateTime.fromISO('2025-01-18')

    assert.isTrue(multiDaySlot.isActiveOnDate(day1), 'Should be active on first day')
    assert.isTrue(multiDaySlot.isActiveOnDate(day2), 'Should be active on middle day')
    assert.isTrue(multiDaySlot.isActiveOnDate(day3), 'Should be active on last day')
    assert.isFalse(multiDaySlot.isActiveOnDate(day4), 'Should not be active after end')

    // Very short specific slot (1 minute)
    const shortSlot = await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'Very Short Meeting',
      specificStartDate: DateTime.fromISO('2025-01-15T14:00:00Z'),
      specificEndDate: DateTime.fromISO('2025-01-15T14:01:00Z'), // 1 minute
      isAvailable: false,
      visibilityScope: 'public',
    })

    const testDay = DateTime.fromISO('2025-01-15')
    assert.isTrue(shortSlot.isActiveOnDate(testDay), 'Should be active even for very short duration')

    const timeRange = shortSlot.getTimeRangeForDate(testDay)
    assert.exists(timeRange, 'Should return time range for short slot')
    assert.equal(timeRange!.start.hour, 14, 'Should have correct start hour')
    assert.equal(timeRange!.end.minute, 1, 'Should have correct end minute')
  })

  /**
   * Test invalid data handling
   */
  test('handles invalid slot data gracefully', async ({ assert }) => {
    // Test invalid day of week
    await assert.rejects(
      () => UserAvailabilitySlot.create({
        userId: user.id,
        title: 'Invalid Day',
        dayOfWeek: 8, // Invalid (should be 0-6)
        startTime: '09:00',
        endTime: '17:00',
        isAvailable: true,
        visibilityScope: 'public',
      }),
      'Should reject invalid day of week'
    )

    // Test invalid time format (handled by validation)
    await assert.rejects(
      () => service.createSlot(user.id, {
        title: 'Invalid Time',
        dayOfWeek: 1,
        startTime: '25:00', // Invalid hour
        endTime: '17:00',
        isAvailable: true,
        visibilityScope: 'public' as AvailabilityScopeType,
      }),
      'Should reject invalid time format'
    )

    // Test missing required fields
    await assert.rejects(
      () => service.createSlot(user.id, {
        title: '', // Empty title
        dayOfWeek: 1,
        startTime: '09:00',
        endTime: '17:00',
        isAvailable: true,
        visibilityScope: 'public' as AvailabilityScopeType,
      }),
      'Should reject empty title'
    )
  })

  /**
   * Test boundary conditions
   */
  test('handles boundary conditions correctly', async ({ assert }) => {
    // Test midnight boundary
    const midnightSlot = await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'Midnight Slot',
      dayOfWeek: 1, // Monday
      startTime: '00:00',
      endTime: '00:01',
      isAvailable: true,
      visibilityScope: 'public',
    })

    const monday = DateTime.fromISO('2025-01-13')
    const timeRange = midnightSlot.getTimeRangeForDate(monday)
    assert.exists(timeRange, 'Should handle midnight start time')
    assert.equal(timeRange!.start.hour, 0, 'Should have midnight start')
    assert.equal(timeRange!.start.minute, 0, 'Should have zero minutes')

    // Test end of day boundary
    const endOfDaySlot = await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'End of Day Slot',
      dayOfWeek: 1, // Monday
      startTime: '23:59',
      endTime: '23:59',
      isAvailable: true,
      visibilityScope: 'public',
    })

    const endTimeRange = endOfDaySlot.getTimeRangeForDate(monday)
    assert.exists(endTimeRange, 'Should handle end of day time')
    assert.equal(endTimeRange!.start.hour, 23, 'Should have correct end hour')
    assert.equal(endTimeRange!.start.minute, 59, 'Should have correct end minute')
  })

  /**
   * Test large dataset handling
   */
  test('handles large numbers of availability slots efficiently', async ({ assert }) => {
    // Create 100 availability slots
    const slots = []
    for (let i = 0; i < 100; i++) {
      const slot = await UserAvailabilitySlot.create({
        userId: user.id,
        title: `Slot ${i}`,
        dayOfWeek: i % 7, // Distribute across all days
        startTime: `${9 + (i % 8)}:00`, // Vary start times
        endTime: `${10 + (i % 8)}:00`,
        isAvailable: i % 2 === 0, // Alternate available/unavailable
        visibilityScope: 'public',
      })
      slots.push(slot)
    }

    // Query should handle large dataset efficiently
    const monday = DateTime.fromISO('2025-01-13') // Monday (dayOfWeek = 1)
    const startTime = process.hrtime.bigint()
    
    const availability = await availabilityService.getAvailabilityForDay(
      user.id,
      user.id,
      monday
    )
    
    const endTime = process.hrtime.bigint()
    const responseTimeMs = Number(endTime - startTime) / 1_000_000

    // Should return only Monday slots (approximately 100/7 ≈ 14 slots)
    assert.isAbove(availability.generalAvailability.length, 10, 'Should return multiple Monday slots')
    assert.isBelow(availability.generalAvailability.length, 20, 'Should not return all slots')
    assert.isBelow(responseTimeMs, 100, 'Should handle large dataset efficiently')

    console.log(`Large dataset query: ${availability.generalAvailability.length} slots in ${responseTimeMs.toFixed(2)}ms`)
  })

  /**
   * Test concurrent modification scenarios
   */
  test('handles concurrent slot modifications correctly', async ({ assert }) => {
    // Create initial slot
    const slot = await service.createSlot(user.id, {
      title: 'Concurrent Test Slot',
      dayOfWeek: 1,
      startTime: '09:00',
      endTime: '17:00',
      isAvailable: true,
      visibilityScope: 'public' as AvailabilityScopeType,
    })

    // Simulate concurrent updates
    const updatePromises = [
      service.updateSlot(slot.id, user.id, { title: 'Updated Title 1' }),
      service.updateSlot(slot.id, user.id, { title: 'Updated Title 2' }),
      service.updateSlot(slot.id, user.id, { startTime: '10:00' }),
      service.updateSlot(slot.id, user.id, { endTime: '16:00' }),
    ]

    // All updates should complete without errors
    const results = await Promise.all(updatePromises)
    assert.equal(results.length, 4, 'All concurrent updates should complete')

    // Final state should be consistent
    const finalSlot = await service.getSlotById(slot.id, user.id)
    assert.exists(finalSlot, 'Slot should still exist after concurrent updates')
    assert.isString(finalSlot.title, 'Title should be a string')
  })

  /**
   * Test error recovery scenarios
   */
  test('recovers gracefully from database errors', async ({ assert }) => {
    // Test querying non-existent user
    const nonExistentUserId = 'non-existent-user-id'
    
    await assert.rejects(
      () => service.createSlot(nonExistentUserId, {
        title: 'Test Slot',
        dayOfWeek: 1,
        startTime: '09:00',
        endTime: '17:00',
        isAvailable: true,
        visibilityScope: 'public' as AvailabilityScopeType,
      }),
      'Should reject creation for non-existent user'
    )

    // Test updating non-existent slot
    await assert.rejects(
      () => service.updateSlot('non-existent-slot-id', user.id, {
        title: 'Updated Title',
      }),
      'Should reject update for non-existent slot'
    )

    // Test deleting non-existent slot
    await assert.rejects(
      () => service.deleteSlot('non-existent-slot-id', user.id),
      'Should reject deletion for non-existent slot'
    )
  })

  /**
   * Test data consistency edge cases
   */
  test('maintains data consistency in edge cases', async ({ assert }) => {
    // Create slot and immediately delete it
    const slot = await service.createSlot(user.id, {
      title: 'Quick Delete Test',
      dayOfWeek: 1,
      startTime: '09:00',
      endTime: '17:00',
      isAvailable: true,
      visibilityScope: 'public' as AvailabilityScopeType,
    })

    await service.deleteSlot(slot.id, user.id)

    // Should not appear in user's slots
    const userSlots = await service.getUserSlots(user.id)
    const deletedSlot = userSlots.find(s => s.id === slot.id)
    assert.notExists(deletedSlot, 'Deleted slot should not appear in user slots')

    // Should not appear in availability queries
    const monday = DateTime.fromISO('2025-01-13')
    const availability = await availabilityService.getAvailabilityForDay(
      user.id,
      user.id,
      monday
    )
    
    const availabilitySlot = availability.generalAvailability.find(s => s.id === slot.id)
    assert.notExists(availabilitySlot, 'Deleted slot should not appear in availability')
  })

  /**
   * Test extreme date ranges
   */
  test('handles extreme date ranges correctly', async ({ assert }) => {
    // Test very far future date
    const farFutureSlot = await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'Far Future Slot',
      specificStartDate: DateTime.fromISO('2099-12-31T12:00:00Z'),
      specificEndDate: DateTime.fromISO('2099-12-31T13:00:00Z'),
      isAvailable: true,
      visibilityScope: 'public',
    })

    const farFutureDate = DateTime.fromISO('2099-12-31')
    assert.isTrue(farFutureSlot.isActiveOnDate(farFutureDate), 'Should handle far future dates')

    // Test very old date
    const oldSlot = await UserAvailabilitySlot.create({
      userId: user.id,
      title: 'Old Slot',
      specificStartDate: DateTime.fromISO('1970-01-01T12:00:00Z'),
      specificEndDate: DateTime.fromISO('1970-01-01T13:00:00Z'),
      isAvailable: true,
      visibilityScope: 'public',
    })

    const oldDate = DateTime.fromISO('1970-01-01')
    assert.isTrue(oldSlot.isActiveOnDate(oldDate), 'Should handle very old dates')
  })
})
