import { test } from '@japa/runner'
import { DateTime } from 'luxon'
import User from '#features/user/user_model'
import Task from '#features/task/task_model'
import Event from '#features/event/event_model'
import UserAvailabilitySlot from '#features/availability/user_availability_slot_model'
import AvailabilityService from '#features/availability/availability_service'
import UserAvailabilitySlotService from '#features/availability/user_availability_slot_service'
import PolicyCategory from '#features/privacy-policy/category/policy_category_model'
import PrivacyPolicyTemplate from '#features/privacy-policy/template/privacy_policy_template_model'
import PolicyTemplateRule from '#features/privacy-policy/rule/policy_template_rule_model'
import PrivacyAssignment from '#models/privacy_assignment'
import UserContact from '#features/contact/user_contact_model'
import UserGroup from '#features/group/user_group_model'
import GroupMember from '#features/group/members/group_member_model'
import { ActivityDetailVisibilityLevel, AvailabilityScopeType } from '#types/policy_template_types'

/**
 * Functional Tests: Availability User Workflows
 * 
 * These tests validate complete user scenarios and business value delivery.
 * They test end-to-end workflows that users would actually perform.
 */

test.group('Availability User Workflows', (group) => {
  let availabilityService: AvailabilityService
  let slotService: UserAvailabilitySlotService
  
  // Test users representing different roles
  let executive: User
  let assistant: User
  let teamLead: User
  let teamMember: User
  let freelancer: User
  let client: User
  let familyMember: User
  
  // Test date for consistent testing
  const testDate = DateTime.fromISO('2025-01-15') // Wednesday

  group.setup(async () => {
    availabilityService = new AvailabilityService()
    slotService = new UserAvailabilitySlotService()
  })

  group.each.setup(async () => {
    // Create test users
    executive = await User.create({
      email: '<EMAIL>',
      password: 'password123',
    })
    
    assistant = await User.create({
      email: '<EMAIL>', 
      password: 'password123',
    })
    
    teamLead = await User.create({
      email: '<EMAIL>',
      password: 'password123',
    })
    
    teamMember = await User.create({
      email: '<EMAIL>',
      password: 'password123',
    })
    
    freelancer = await User.create({
      email: '<EMAIL>',
      password: 'password123',
    })
    
    client = await User.create({
      email: '<EMAIL>',
      password: 'password123',
    })
    
    familyMember = await User.create({
      email: '<EMAIL>',
      password: 'password123',
    })
  })

  /**
   * SCENARIO 1: Executive Assistant Coordination
   * Business Value: EA can schedule meetings efficiently while respecting executive privacy
   */
  test('Executive Assistant can see executive availability with appropriate privacy', async ({ assert }) => {
    // Setup: Create contact relationship between executive and assistant
    await UserContact.create({
      userId: executive.id,
      contactUserId: assistant.id,
      status: 'accepted',
    })

    // Setup: Executive creates privacy template for assistant access
    const workCategory = await PolicyCategory.create({
      userId: executive.id,
      key: 'work_hours',
      label: 'Work Hours',
      description: 'Professional work time',
    })

    const assistantTemplate = await PrivacyPolicyTemplate.create({
      userId: executive.id,
      name: 'Assistant Access',
      description: 'Template for assistant to see work schedule',
      categoryId: workCategory.id,
      blocksScheduling: true,
      defaultDetailVisibility: ActivityDetailVisibilityLevel.BUSY_ONLY,
    })

    // Rule: Contacts can see title only for work activities
    await PolicyTemplateRule.create({
      userId: executive.id,
      templateId: assistantTemplate.id,
      viewerScopeType: 'allContacts',
      blocksScheduling: true,
      detailVisibility: ActivityDetailVisibilityLevel.TITLE_ONLY,
      priority: 1,
    })

    // Setup: Executive has work meeting and personal appointment
    const workMeeting = await Task.create({
      userId: executive.id,
      title: 'Board Meeting',
      description: 'Quarterly board review - confidential',
      startDate: testDate.set({ hour: 10, minute: 0 }).toJSDate(),
      endDate: testDate.set({ hour: 11, minute: 0 }).toJSDate(),
    })

    const personalAppointment = await Event.create({
      userId: executive.id,
      title: 'Doctor Appointment',
      description: 'Annual physical checkup',
      startDate: testDate.set({ hour: 14, minute: 0 }).toJSDate(),
      endDate: testDate.set({ hour: 15, minute: 0 }).toJSDate(),
    })

    // Assign privacy templates
    await PrivacyAssignment.create({
      taskId: workMeeting.id,
      templateId: assistantTemplate.id,
    })

    await PrivacyAssignment.create({
      eventId: personalAppointment.id,
      templateId: assistantTemplate.id,
    })

    // Setup: Executive's general availability (9am-5pm weekdays)
    await UserAvailabilitySlot.create({
      userId: executive.id,
      title: 'Work Hours',
      dayOfWeek: 3, // Wednesday
      startTime: '09:00',
      endTime: '17:00',
      isAvailable: true,
      visibilityScope: 'all_contacts',
    })

    // Test: Assistant queries executive's availability
    const availability = await availabilityService.getAvailabilityForDay(
      assistant.id,
      executive.id,
      testDate
    )

    // Assertions: Business value validation
    assert.equal(availability.activities.length, 2, 'Assistant should see both activities')
    
    const boardMeeting = availability.activities.find(a => a.id === workMeeting.id)
    assert.exists(boardMeeting, 'Assistant should see work meeting')
    assert.equal(boardMeeting.title, 'Board Meeting', 'Assistant should see meeting title')
    assert.isNull(boardMeeting.description, 'Assistant should NOT see confidential description')
    assert.isTrue(boardMeeting.blocksScheduling, 'Meeting should block scheduling')

    const doctorAppt = availability.activities.find(a => a.id === personalAppointment.id)
    assert.exists(doctorAppt, 'Assistant should see personal appointment')
    assert.equal(doctorAppt.title, 'Doctor Appointment', 'Assistant should see appointment title')
    assert.isNull(doctorAppt.description, 'Assistant should NOT see personal details')

    assert.equal(availability.generalAvailability.length, 1, 'Assistant should see work hours')
    assert.equal(availability.generalAvailability[0].title, 'Work Hours')
  })

  /**
   * SCENARIO 2: Team Collaboration
   * Business Value: Team members coordinate effectively with appropriate visibility
   */
  test('Team members can coordinate with group-based privacy rules', async ({ assert }) => {
    // Setup: Create team group
    const devTeam = await UserGroup.create({
      userId: teamLead.id,
      name: 'Development Team',
      description: 'Core development team',
    })

    // Add team member to group
    await GroupMember.create({
      groupId: devTeam.id,
      userId: teamMember.id,
      role: 'member',
    })

    // Setup: Team member creates privacy template for team collaboration
    const teamTemplate = await PrivacyPolicyTemplate.create({
      userId: teamMember.id,
      name: 'Team Collaboration',
      description: 'Full visibility for team members',
      blocksScheduling: false, // Team can schedule during work activities
      defaultDetailVisibility: ActivityDetailVisibilityLevel.BUSY_ONLY,
    })

    // Rule: Team group gets full details
    await PolicyTemplateRule.create({
      userId: teamMember.id,
      templateId: teamTemplate.id,
      viewerScopeType: 'specificGroup',
      viewerTargetGroupId: devTeam.id,
      blocksScheduling: false,
      detailVisibility: ActivityDetailVisibilityLevel.FULL_DETAILS,
      priority: 1,
    })

    // Setup: Team member has development work and personal task
    const devWork = await Task.create({
      userId: teamMember.id,
      title: 'Feature Implementation',
      description: 'Implementing user authentication feature',
      startDate: testDate.set({ hour: 9, minute: 0 }).toJSDate(),
      endDate: testDate.set({ hour: 12, minute: 0 }).toJSDate(),
    })

    const personalTask = await Task.create({
      userId: teamMember.id,
      title: 'Personal Research',
      description: 'Learning new framework',
      startDate: testDate.set({ hour: 13, minute: 0 }).toJSDate(),
      endDate: testDate.set({ hour: 14, minute: 0 }).toJSDate(),
    })

    // Assign privacy template
    await PrivacyAssignment.create({
      taskId: devWork.id,
      templateId: teamTemplate.id,
    })

    await PrivacyAssignment.create({
      taskId: personalTask.id,
      templateId: teamTemplate.id,
    })

    // Test: Team lead queries team member's availability
    const availability = await availabilityService.getAvailabilityForDay(
      teamLead.id,
      teamMember.id,
      testDate
    )

    // Assertions: Team collaboration validation
    assert.equal(availability.activities.length, 2, 'Team lead should see both activities')
    
    const devActivity = availability.activities.find(a => a.id === devWork.id)
    assert.exists(devActivity, 'Team lead should see development work')
    assert.equal(devActivity.title, 'Feature Implementation')
    assert.equal(devActivity.description, 'Implementing user authentication feature')
    assert.isFalse(devActivity.blocksScheduling, 'Team can schedule during work activities')

    const personalActivity = availability.activities.find(a => a.id === personalTask.id)
    assert.exists(personalActivity, 'Team lead should see personal task')
    assert.equal(personalActivity.title, 'Personal Research')
    assert.equal(personalActivity.description, 'Learning new framework')
  })

  /**
   * SCENARIO 3: Client-Freelancer Professional Scheduling
   * Business Value: Professional availability sharing without revealing existing client work
   */
  test('Freelancer can share availability with clients while protecting existing work', async ({ assert }) => {
    // Setup: Create contact relationship
    await UserContact.create({
      userId: freelancer.id,
      contactUserId: client.id,
      status: 'accepted',
    })

    // Setup: Freelancer creates client-facing privacy template
    const clientTemplate = await PrivacyPolicyTemplate.create({
      userId: freelancer.id,
      name: 'Client Visibility',
      description: 'What clients can see',
      blocksScheduling: true,
      defaultDetailVisibility: ActivityDetailVisibilityLevel.BUSY_ONLY,
    })

    // Rule: Specific contacts see busy only
    await PolicyTemplateRule.create({
      userId: freelancer.id,
      templateId: clientTemplate.id,
      viewerScopeType: 'specificContact',
      viewerTargetUserId: client.id,
      blocksScheduling: true,
      detailVisibility: ActivityDetailVisibilityLevel.BUSY_ONLY,
      customMessage: 'Busy with client work',
      priority: 1,
    })

    // Setup: Freelancer has existing client work and available slots
    const existingClientWork = await Task.create({
      userId: freelancer.id,
      title: 'Client A Project',
      description: 'Confidential project for existing client',
      startDate: testDate.set({ hour: 9, minute: 0 }).toJSDate(),
      endDate: testDate.set({ hour: 12, minute: 0 }).toJSDate(),
    })

    await PrivacyAssignment.create({
      taskId: existingClientWork.id,
      templateId: clientTemplate.id,
    })

    // Setup: Available time slots for new clients
    await UserAvailabilitySlot.create({
      userId: freelancer.id,
      title: 'Available for New Projects',
      dayOfWeek: 3, // Wednesday
      startTime: '14:00',
      endTime: '17:00',
      isAvailable: true,
      visibilityScope: 'all_contacts',
    })

    // Test: Client queries freelancer's availability
    const availability = await availabilityService.getAvailabilityForDay(
      client.id,
      freelancer.id,
      testDate
    )

    // Assertions: Professional privacy validation
    assert.equal(availability.activities.length, 1, 'Client should see existing work as busy block')
    
    const busyBlock = availability.activities[0]
    assert.equal(busyBlock.title, 'Busy with client work', 'Should show custom busy message')
    assert.isNull(busyBlock.description, 'Should not reveal project details')
    assert.isTrue(busyBlock.blocksScheduling, 'Should block scheduling during existing work')

    assert.equal(availability.generalAvailability.length, 1, 'Client should see available slots')
    assert.equal(availability.generalAvailability[0].title, 'Available for New Projects')
    assert.isTrue(availability.generalAvailability[0].isAvailable)
  })

  /**
   * SCENARIO 4: Work-Life Balance Privacy
   * Business Value: Appropriate context sharing across different relationship types
   */
  test('User can maintain work-life balance with relationship-based privacy', async ({ assert }) => {
    // Setup: Create relationships
    await UserContact.create({
      userId: teamMember.id,
      contactUserId: teamLead.id,
      status: 'accepted',
    })

    await UserContact.create({
      userId: teamMember.id,
      contactUserId: familyMember.id,
      status: 'accepted',
    })

    // Setup: Work privacy template
    const workTemplate = await PrivacyPolicyTemplate.create({
      userId: teamMember.id,
      name: 'Work Privacy',
      description: 'Work-related activities',
      blocksScheduling: true,
      defaultDetailVisibility: ActivityDetailVisibilityLevel.BUSY_ONLY,
    })

    // Rule: Work contacts see work details
    await PolicyTemplateRule.create({
      userId: teamMember.id,
      templateId: workTemplate.id,
      viewerScopeType: 'specificContact',
      viewerTargetUserId: teamLead.id,
      blocksScheduling: true,
      detailVisibility: ActivityDetailVisibilityLevel.FULL_DETAILS,
      priority: 1,
    })

    // Setup: Personal privacy template
    const personalTemplate = await PrivacyPolicyTemplate.create({
      userId: teamMember.id,
      name: 'Personal Privacy',
      description: 'Personal activities',
      blocksScheduling: true,
      defaultDetailVisibility: ActivityDetailVisibilityLevel.HIDDEN,
    })

    // Rule: Family sees personal details
    await PolicyTemplateRule.create({
      userId: teamMember.id,
      templateId: personalTemplate.id,
      viewerScopeType: 'specificContact',
      viewerTargetUserId: familyMember.id,
      blocksScheduling: false,
      detailVisibility: ActivityDetailVisibilityLevel.FULL_DETAILS,
      priority: 1,
    })

    // Setup: Work and personal activities
    const workMeeting = await Task.create({
      userId: teamMember.id,
      title: 'Sprint Planning',
      description: 'Planning next sprint with team',
      startDate: testDate.set({ hour: 10, minute: 0 }).toJSDate(),
      endDate: testDate.set({ hour: 11, minute: 0 }).toJSDate(),
    })

    const familyDinner = await Event.create({
      userId: teamMember.id,
      title: 'Family Dinner',
      description: 'Dinner with parents',
      startDate: testDate.set({ hour: 18, minute: 0 }).toJSDate(),
      endDate: testDate.set({ hour: 20, minute: 0 }).toJSDate(),
    })

    await PrivacyAssignment.create({
      taskId: workMeeting.id,
      templateId: workTemplate.id,
    })

    await PrivacyAssignment.create({
      eventId: familyDinner.id,
      templateId: personalTemplate.id,
    })

    // Test: Team lead queries availability (should see work, not personal)
    const workAvailability = await availabilityService.getAvailabilityForDay(
      teamLead.id,
      teamMember.id,
      testDate
    )

    assert.equal(workAvailability.activities.length, 1, 'Team lead should only see work activity')
    assert.equal(workAvailability.activities[0].title, 'Sprint Planning')
    assert.equal(workAvailability.activities[0].description, 'Planning next sprint with team')

    // Test: Family member queries availability (should see personal, not work details)
    const familyAvailability = await availabilityService.getAvailabilityForDay(
      familyMember.id,
      teamMember.id,
      testDate
    )

    assert.equal(familyAvailability.activities.length, 2, 'Family should see both activities')
    
    const workActivity = familyAvailability.activities.find(a => a.id === workMeeting.id)
    assert.equal(workActivity.title, 'Busy', 'Family should see work as busy')
    assert.isNull(workActivity.description, 'Family should not see work details')

    const familyActivity = familyAvailability.activities.find(a => a.id === familyDinner.id)
    assert.equal(familyActivity.title, 'Family Dinner')
    assert.equal(familyActivity.description, 'Dinner with parents')
  })
})
