import vine from '@vinejs/vine'
import { AvailabilityScopeType } from '#types/policy_template_types'

/**
 * Validator for creating availability slots
 */
export const createAvailabilitySlotValidator = vine.compile(
  vine.object({
    title: vine.string().trim().minLength(1).maxLength(255),
    description: vine.string().trim().maxLength(1000).optional(),

    // Recurring availability fields
    dayOfWeek: vine.number().min(0).max(6).optional(), // 0-6 (Sunday-Saturday)
    startTime: vine.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(), // HH:MM format
    endTime: vine.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(), // HH:MM format
    recurringStartDate: vine.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(), // YYYY-MM-DD
    recurringEndDate: vine.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(), // YYYY-MM-DD

    // Specific availability fields
    specificStartDate: vine.string().datetime().optional(), // ISO 8601
    specificEndDate: vine.string().datetime().optional(), // ISO 8601

    // State and visibility
    isAvailable: vine.boolean().optional(),
    visibilityScope: vine.enum(Object.values(AvailabilityScopeType)).optional(),
    visibilityTargetUserId: vine.string().uuid().optional(),
    visibilityTargetGroupId: vine.string().uuid().optional(),
    visibilityTargetContactListId: vine.string().uuid().optional(),
  })
)

/**
 * Validator for updating availability slots
 */
export const updateAvailabilitySlotValidator = vine.compile(
  vine.object({
    title: vine.string().trim().minLength(1).maxLength(255).optional(),
    description: vine.string().trim().maxLength(1000).optional(),

    // Recurring availability fields
    dayOfWeek: vine.number().min(0).max(6).optional(),
    startTime: vine.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
    endTime: vine.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
    recurringStartDate: vine.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
    recurringEndDate: vine.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),

    // Specific availability fields
    specificStartDate: vine.string().datetime().optional(),
    specificEndDate: vine.string().datetime().optional(),

    // State and visibility
    isAvailable: vine.boolean().optional(),
    visibilityScope: vine.enum(Object.values(AvailabilityScopeType)).optional(),
    visibilityTargetUserId: vine.string().uuid().optional(),
    visibilityTargetGroupId: vine.string().uuid().optional(),
    visibilityTargetContactListId: vine.string().uuid().optional(),
  })
)

/**
 * Validator for availability query parameters
 */
export const availabilityQueryValidator = vine.compile(
  vine.object({
    startDate: vine.string().regex(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD
    endDate: vine.string().regex(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD
  })
)

/**
 * Validator for availability slot query parameters
 */
export const availabilitySlotQueryValidator = vine.compile(
  vine.object({
    slotType: vine.enum(['recurring', 'specific']).optional(),
    isAvailable: vine.boolean().optional(),
    visibilityScope: vine.enum(Object.values(AvailabilityScopeType)).optional(),
    dayOfWeek: vine.number().min(0).max(6).optional(),
    page: vine.number().min(1).optional(),
    limit: vine.number().min(1).max(100).optional(),
  })
)

/**
 * Validator for availability check requests
 */
export const availabilityCheckValidator = vine.compile(
  vine.object({
    targetUserId: vine.string().uuid(),
    startDate: vine.string().datetime(), // ISO 8601
    endDate: vine.string().datetime(), // ISO 8601
  })
)
