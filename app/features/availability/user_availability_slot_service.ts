import { DateTime } from 'luxon'
import UserAvailabilitySlot from './user_availability_slot_model.js'
import User from '#features/user/user_model'
import AvailabilityService from './availability_service.js'
import { notFoundException } from '#utils/error'
import { AvailabilityScopeType } from '#types/policy_template_types'

interface CreateSlotData {
  title: string
  description?: string
  // Recurring availability
  dayOfWeek?: number
  startTime?: string
  endTime?: string
  recurringStartDate?: string
  recurringEndDate?: string
  // Specific availability
  specificStartDate?: string
  specificEndDate?: string
  // State and visibility
  isAvailable?: boolean
  visibilityScope?: AvailabilityScopeType
  visibilityTargetUserId?: string
  visibilityTargetGroupId?: string
  visibilityTargetContactListId?: string
}

interface UpdateSlotData extends Partial<CreateSlotData> {}

interface SlotQuery {
  slotType?: 'recurring' | 'specific'
  isAvailable?: boolean
  visibilityScope?: AvailabilityScopeType
  dayOfWeek?: number
  page?: number
  limit?: number
}

/**
 * UserAvailabilitySlotService
 * 
 * Service for managing user availability slots with validation and business logic.
 * Handles both recurring and specific availability slots with privacy controls.
 */
export default class UserAvailabilitySlotService {
  private availabilityService = new AvailabilityService()

  /**
   * Create a new availability slot for a user
   */
  async createSlot(userId: string, data: CreateSlotData): Promise<UserAvailabilitySlot> {
    // Verify user exists
    await User.findOrFail(userId)

    // Validate slot type consistency
    this.validateSlotTypeData(data)

    // Validate time ordering
    this.validateTimeOrdering(data)

    // Create the slot
    const slot = await UserAvailabilitySlot.create({
      userId,
      title: data.title,
      description: data.description || null,
      dayOfWeek: data.dayOfWeek || null,
      startTime: data.startTime || null,
      endTime: data.endTime || null,
      recurringStartDate: data.recurringStartDate ? DateTime.fromISO(data.recurringStartDate) : null,
      recurringEndDate: data.recurringEndDate ? DateTime.fromISO(data.recurringEndDate) : null,
      specificStartDate: data.specificStartDate ? DateTime.fromISO(data.specificStartDate) : null,
      specificEndDate: data.specificEndDate ? DateTime.fromISO(data.specificEndDate) : null,
      isAvailable: data.isAvailable ?? true,
      visibilityScope: data.visibilityScope || 'public',
      visibilityTargetUserId: data.visibilityTargetUserId || null,
      visibilityTargetGroupId: data.visibilityTargetGroupId || null,
      visibilityTargetContactListId: data.visibilityTargetContactListId || null,
    })

    return slot
  }

  /**
   * Update an existing availability slot
   */
  async updateSlot(id: string, userId: string, data: UpdateSlotData): Promise<UserAvailabilitySlot> {
    const slot = await UserAvailabilitySlot.query()
      .where('id', id)
      .where('userId', userId)
      .whereNull('deletedAt')
      .first()

    if (!slot) {
      throw notFoundException('Availability slot not found')
    }

    // If updating slot type data, validate consistency
    if (this.isSlotTypeDataPresent(data)) {
      this.validateSlotTypeData(data)
    }

    // If updating time data, validate ordering
    if (this.isTimeDataPresent(data)) {
      this.validateTimeOrdering(data)
    }

    // Update the slot
    slot.merge({
      title: data.title ?? slot.title,
      description: data.description ?? slot.description,
      dayOfWeek: data.dayOfWeek ?? slot.dayOfWeek,
      startTime: data.startTime ?? slot.startTime,
      endTime: data.endTime ?? slot.endTime,
      recurringStartDate: data.recurringStartDate ? DateTime.fromISO(data.recurringStartDate) : slot.recurringStartDate,
      recurringEndDate: data.recurringEndDate ? DateTime.fromISO(data.recurringEndDate) : slot.recurringEndDate,
      specificStartDate: data.specificStartDate ? DateTime.fromISO(data.specificStartDate) : slot.specificStartDate,
      specificEndDate: data.specificEndDate ? DateTime.fromISO(data.specificEndDate) : slot.specificEndDate,
      isAvailable: data.isAvailable ?? slot.isAvailable,
      visibilityScope: data.visibilityScope ?? slot.visibilityScope,
      visibilityTargetUserId: data.visibilityTargetUserId ?? slot.visibilityTargetUserId,
      visibilityTargetGroupId: data.visibilityTargetGroupId ?? slot.visibilityTargetGroupId,
      visibilityTargetContactListId: data.visibilityTargetContactListId ?? slot.visibilityTargetContactListId,
    })

    await slot.save()
    return slot
  }

  /**
   * Delete (soft delete) an availability slot
   */
  async deleteSlot(id: string, userId: string): Promise<void> {
    const slot = await UserAvailabilitySlot.query()
      .where('id', id)
      .where('userId', userId)
      .whereNull('deletedAt')
      .first()

    if (!slot) {
      throw notFoundException('Availability slot not found')
    }

    await slot.delete()
  }

  /**
   * Get a specific availability slot by ID
   */
  async getSlotById(id: string, userId: string): Promise<UserAvailabilitySlot> {
    const slot = await UserAvailabilitySlot.query()
      .where('id', id)
      .where('userId', userId)
      .whereNull('deletedAt')
      .first()

    if (!slot) {
      throw notFoundException('Availability slot not found')
    }

    return slot
  }

  /**
   * Get user's availability slots with optional filtering
   */
  async getUserSlots(userId: string, query: SlotQuery = {}): Promise<UserAvailabilitySlot[]> {
    const slotsQuery = UserAvailabilitySlot.query()
      .where('userId', userId)
      .whereNull('deletedAt')

    // Apply filters
    if (query.slotType === 'recurring') {
      slotsQuery.whereNotNull('dayOfWeek')
    } else if (query.slotType === 'specific') {
      slotsQuery.whereNotNull('specificStartDate')
    }

    if (query.isAvailable !== undefined) {
      slotsQuery.where('isAvailable', query.isAvailable)
    }

    if (query.visibilityScope) {
      slotsQuery.where('visibilityScope', query.visibilityScope)
    }

    if (query.dayOfWeek !== undefined) {
      slotsQuery.where('dayOfWeek', query.dayOfWeek)
    }

    // Apply pagination
    const page = query.page || 1
    const limit = Math.min(query.limit || 50, 100) // Max 100 items per page

    const slots = await slotsQuery
      .orderBy('dayOfWeek', 'asc')
      .orderBy('startTime', 'asc')
      .orderBy('specificStartDate', 'asc')
      .paginate(page, limit)

    return slots.all()
  }

  /**
   * Check if a user is available during a specific time period
   * This integrates with the privacy system to respect visibility rules
   */
  async checkUserAvailability(
    requesterUserId: string,
    targetUserId: string,
    startDate: DateTime,
    endDate: DateTime
  ): Promise<boolean> {
    // Get availability data for the date range using the privacy-aware service
    const availabilityResults = await this.availabilityService.getAvailabilityForDateRange(
      requesterUserId,
      targetUserId,
      startDate.startOf('day'),
      endDate.startOf('day')
    )

    // Check each day in the range
    for (const dayResult of availabilityResults) {
      // Check if any activities block scheduling during the requested time
      const conflictingActivities = dayResult.activities.filter((activity) => {
        if (!activity.blocksScheduling) return false

        const activityStart = DateTime.fromJSDate(activity.startDate)
        const activityEnd = DateTime.fromJSDate(activity.endDate)

        // Check for time overlap
        return startDate < activityEnd && endDate > activityStart
      })

      if (conflictingActivities.length > 0) {
        return false // User is not available due to conflicting activities
      }

      // Check general availability slots
      const availableSlots = dayResult.generalAvailability.filter((slot) => slot.isAvailable)
      const unavailableSlots = dayResult.generalAvailability.filter((slot) => !slot.isAvailable)

      // Check if any unavailable slots conflict with the requested time
      for (const slot of unavailableSlots) {
        const currentDate = DateTime.fromISO(dayResult.date)
        const slotTimeRange = slot.getTimeRangeForDate(currentDate)

        if (slotTimeRange) {
          // Check for time overlap with unavailable slot
          if (startDate < slotTimeRange.end && endDate > slotTimeRange.start) {
            return false // User is explicitly unavailable during this time
          }
        }
      }
    }

    return true // User appears to be available
  }

  /**
   * Validate that slot type data is consistent (either recurring or specific, not both)
   */
  private validateSlotTypeData(data: CreateSlotData | UpdateSlotData): void {
    const hasRecurring = data.dayOfWeek !== undefined || data.startTime || data.endTime
    const hasSpecific = data.specificStartDate || data.specificEndDate

    if (hasRecurring && hasSpecific) {
      throw new Error('Cannot specify both recurring and specific availability in the same slot')
    }

    if (!hasRecurring && !hasSpecific) {
      throw new Error('Must specify either recurring or specific availability')
    }
  }

  /**
   * Validate time ordering for both recurring and specific slots
   */
  private validateTimeOrdering(data: CreateSlotData | UpdateSlotData): void {
    // Validate recurring time order
    if (data.startTime && data.endTime) {
      if (data.startTime >= data.endTime) {
        throw new Error('Start time must be before end time')
      }
    }

    // Validate specific date order
    if (data.specificStartDate && data.specificEndDate) {
      const start = DateTime.fromISO(data.specificStartDate)
      const end = DateTime.fromISO(data.specificEndDate)

      if (start >= end) {
        throw new Error('Specific start date must be before end date')
      }
    }

    // Validate recurring date range
    if (data.recurringStartDate && data.recurringEndDate) {
      const start = DateTime.fromISO(data.recurringStartDate)
      const end = DateTime.fromISO(data.recurringEndDate)

      if (start > end) {
        throw new Error('Recurring start date must be before or equal to end date')
      }
    }
  }

  /**
   * Check if slot type data is present in the update data
   */
  private isSlotTypeDataPresent(data: UpdateSlotData): boolean {
    return !!(
      data.dayOfWeek !== undefined ||
      data.startTime ||
      data.endTime ||
      data.specificStartDate ||
      data.specificEndDate
    )
  }

  /**
   * Check if time data is present in the update data
   */
  private isTimeDataPresent(data: UpdateSlotData): boolean {
    return !!(
      data.startTime ||
      data.endTime ||
      data.specificStartDate ||
      data.specificEndDate ||
      data.recurringStartDate ||
      data.recurringEndDate
    )
  }
}
