import { DateTime } from 'luxon'
import redis from '@adonisjs/redis/services/main'

interface RelationshipContext {
  isContact: boolean
  groupIds: string[]
  contactListIds: string[]
  fromCache: boolean
}

/**
 * AvailabilityCacheService
 * 
 * Handles Redis caching for the availability system to achieve:
 * - Cache Hit: 2 queries, <50ms response time, >90% hit rate
 * - Cache Miss: 3 queries, <150ms response time
 * 
 * Caching Strategy:
 * 1. Relationship Context: Cache user relationships (contacts, groups, lists)
 * 2. Activity Data: Cache activity + privacy rule data for short periods
 * 3. Availability Slots: Cache general availability slots
 */
export default class AvailabilityCacheService {
  private readonly RELATIONSHIP_TTL = 3600 // 1 hour
  private readonly ACTIVITY_TTL = 300 // 5 minutes
  private readonly AVAILABILITY_TTL = 1800 // 30 minutes

  /**
   * Get or cache relationship context between two users
   */
  async getOrCacheRelationshipContext(
    requesterUserId: string,
    targetUserId: string,
    fetchFunction: () => Promise<Omit<RelationshipContext, 'fromCache'>>
  ): Promise<RelationshipContext> {
    const cacheKey = `relationship:${requesterUserId}:${targetUserId}`

    try {
      const cached = await redis.get(cacheKey)
      if (cached) {
        const data = JSON.parse(cached)
        return { ...data, fromCache: true }
      }
    } catch (error) {
      console.warn('Redis cache read error for relationship context:', error)
    }

    // Cache miss - fetch fresh data
    const freshData = await fetchFunction()
    const result = { ...freshData, fromCache: false }

    // Cache the result
    try {
      await redis.setex(cacheKey, this.RELATIONSHIP_TTL, JSON.stringify(freshData))
    } catch (error) {
      console.warn('Redis cache write error for relationship context:', error)
    }

    return result
  }

  /**
   * Get or cache activity data for a specific date
   */
  async getOrCacheActivitiesForDate(
    targetUserId: string,
    date: DateTime,
    fetchFunction: () => Promise<any[]>
  ): Promise<any[]> {
    const cacheKey = `activities:${targetUserId}:${date.toISODate()}`

    try {
      const cached = await redis.get(cacheKey)
      if (cached) {
        return JSON.parse(cached)
      }
    } catch (error) {
      console.warn('Redis cache read error for activities:', error)
    }

    // Cache miss - fetch fresh data
    const freshData = await fetchFunction()

    // Cache the result
    try {
      await redis.setex(cacheKey, this.ACTIVITY_TTL, JSON.stringify(freshData))
    } catch (error) {
      console.warn('Redis cache write error for activities:', error)
    }

    return freshData
  }

  /**
   * Get or cache availability slots for a user
   */
  async getOrCacheAvailabilitySlots(
    targetUserId: string,
    date: DateTime,
    fetchFunction: () => Promise<any[]>
  ): Promise<any[]> {
    const cacheKey = `availability_slots:${targetUserId}:${date.toISODate()}`

    try {
      const cached = await redis.get(cacheKey)
      if (cached) {
        return JSON.parse(cached)
      }
    } catch (error) {
      console.warn('Redis cache read error for availability slots:', error)
    }

    // Cache miss - fetch fresh data
    const freshData = await fetchFunction()

    // Cache the result
    try {
      await redis.setex(cacheKey, this.AVAILABILITY_TTL, JSON.stringify(freshData))
    } catch (error) {
      console.warn('Redis cache write error for availability slots:', error)
    }

    return freshData
  }

  /**
   * Batch cache activities for multiple dates (calendar view optimization)
   */
  async batchCacheActivitiesForDateRange(
    targetUserId: string,
    startDate: DateTime,
    endDate: DateTime,
    activities: any[]
  ): Promise<void> {
    const pipeline = redis.pipeline()
    
    // Group activities by date
    const activitiesByDate = new Map<string, any[]>()
    
    activities.forEach((activity) => {
      const activityDate = DateTime.fromJSDate(activity.startDate).toISODate()
      if (!activitiesByDate.has(activityDate)) {
        activitiesByDate.set(activityDate, [])
      }
      activitiesByDate.get(activityDate)!.push(activity)
    })

    // Cache each date's activities
    let currentDate = startDate
    while (currentDate <= endDate) {
      const dateStr = currentDate.toISODate()
      const cacheKey = `activities:${targetUserId}:${dateStr}`
      const dayActivities = activitiesByDate.get(dateStr) || []
      
      pipeline.setex(cacheKey, this.ACTIVITY_TTL, JSON.stringify(dayActivities))
      currentDate = currentDate.plus({ days: 1 })
    }

    try {
      await pipeline.exec()
    } catch (error) {
      console.warn('Redis batch cache error for activities:', error)
    }
  }

  /**
   * Invalidate caches when user data changes
   */
  async invalidateUserCaches(userId: string): Promise<void> {
    try {
      // Get all cache keys related to this user
      const patterns = [
        `relationship:${userId}:*`,
        `relationship:*:${userId}`,
        `activities:${userId}:*`,
        `availability_slots:${userId}:*`,
      ]

      for (const pattern of patterns) {
        const keys = await redis.keys(pattern)
        if (keys.length > 0) {
          await redis.del(...keys)
        }
      }
    } catch (error) {
      console.warn('Redis cache invalidation error:', error)
    }
  }

  /**
   * Invalidate relationship caches when contacts/groups change
   */
  async invalidateRelationshipCaches(userId: string): Promise<void> {
    try {
      const patterns = [
        `relationship:${userId}:*`,
        `relationship:*:${userId}`,
      ]

      for (const pattern of patterns) {
        const keys = await redis.keys(pattern)
        if (keys.length > 0) {
          await redis.del(...keys)
        }
      }
    } catch (error) {
      console.warn('Redis relationship cache invalidation error:', error)
    }
  }

  /**
   * Invalidate activity caches when tasks/events change
   */
  async invalidateActivityCaches(userId: string, date?: DateTime): Promise<void> {
    try {
      if (date) {
        // Invalidate specific date
        const cacheKey = `activities:${userId}:${date.toISODate()}`
        await redis.del(cacheKey)
      } else {
        // Invalidate all activity caches for user
        const pattern = `activities:${userId}:*`
        const keys = await redis.keys(pattern)
        if (keys.length > 0) {
          await redis.del(...keys)
        }
      }
    } catch (error) {
      console.warn('Redis activity cache invalidation error:', error)
    }
  }

  /**
   * Invalidate availability slot caches when slots change
   */
  async invalidateAvailabilitySlotCaches(userId: string): Promise<void> {
    try {
      const pattern = `availability_slots:${userId}:*`
      const keys = await redis.keys(pattern)
      if (keys.length > 0) {
        await redis.del(...keys)
      }
    } catch (error) {
      console.warn('Redis availability slot cache invalidation error:', error)
    }
  }

  /**
   * Get cache statistics for monitoring
   */
  async getCacheStats(): Promise<{
    relationshipCacheSize: number
    activityCacheSize: number
    availabilityCacheSize: number
    totalMemoryUsage: string
  }> {
    try {
      const [relationshipKeys, activityKeys, availabilityKeys, memoryInfo] = await Promise.all([
        redis.keys('relationship:*'),
        redis.keys('activities:*'),
        redis.keys('availability_slots:*'),
        redis.memory('usage'),
      ])

      return {
        relationshipCacheSize: relationshipKeys.length,
        activityCacheSize: activityKeys.length,
        availabilityCacheSize: availabilityKeys.length,
        totalMemoryUsage: memoryInfo || 'unknown',
      }
    } catch (error) {
      console.warn('Redis cache stats error:', error)
      return {
        relationshipCacheSize: 0,
        activityCacheSize: 0,
        availabilityCacheSize: 0,
        totalMemoryUsage: 'error',
      }
    }
  }

  /**
   * Warm up cache for a user's common availability queries
   */
  async warmUpUserCache(
    userId: string,
    fetchRelationships: () => Promise<string[]>,
    fetchActivities: (date: DateTime) => Promise<any[]>,
    fetchAvailability: (date: DateTime) => Promise<any[]>
  ): Promise<void> {
    try {
      // Warm up next 7 days of data
      const today = DateTime.now().startOf('day')
      const promises: Promise<any>[] = []

      for (let i = 0; i < 7; i++) {
        const date = today.plus({ days: i })
        
        // Warm up activities
        promises.push(
          this.getOrCacheActivitiesForDate(userId, date, () => fetchActivities(date))
        )
        
        // Warm up availability slots
        promises.push(
          this.getOrCacheAvailabilitySlots(userId, date, () => fetchAvailability(date))
        )
      }

      await Promise.all(promises)
    } catch (error) {
      console.warn('Cache warm-up error:', error)
    }
  }
}
