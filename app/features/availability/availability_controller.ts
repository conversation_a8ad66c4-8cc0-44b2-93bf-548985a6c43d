import type { HttpContext } from '@adonisjs/core/http'
import { DateTime } from 'luxon'
import AvailabilityService from './availability_service.js'
import UserAvailabilitySlotService from './user_availability_slot_service.js'
import { createSuccessResponse, createErrorResponse } from '#utils/response'
import {
  createAvailabilitySlotValidator,
  updateAvailabilitySlotValidator,
  availabilityQueryValidator,
} from './availability_validators.js'

/**
 * AvailabilityController
 * 
 * Handles HTTP requests for availability information and availability slot management.
 * Integrates with the sophisticated privacy policy system for proper access control.
 */
export default class AvailabilityController {
  private availabilityService = new AvailabilityService()
  private slotService = new UserAvailabilitySlotService()

  /**
   * GET /api/v1/availability/:userId/:date
   * 
   * Get availability information for a target user on a specific date.
   * Applies privacy rules based on the requesting user's relationship to the target.
   */
  async getAvailabilityForDay({ params, auth, response }: HttpContext) {
    try {
      const requesterUserId = auth.user!.id
      const { userId: targetUserId, date: dateParam } = params

      // Validate and parse date
      const date = DateTime.fromISO(dateParam)
      if (!date.isValid) {
        return response.badRequest(
          createErrorResponse('Invalid date format. Use YYYY-MM-DD format.', 'INVALID_DATE')
        )
      }

      const availability = await this.availabilityService.getAvailabilityForDay(
        requesterUserId,
        targetUserId,
        date
      )

      return response.ok(createSuccessResponse(availability, 'Availability retrieved successfully'))
    } catch (error) {
      return response.internalServerError(
        createErrorResponse('Failed to retrieve availability', 'AVAILABILITY_ERROR', error.message)
      )
    }
  }

  /**
   * GET /api/v1/availability/:userId/range
   * 
   * Get availability information for a target user across a date range.
   * Optimized for calendar views with batch processing.
   */
  async getAvailabilityForRange({ params, request, auth, response }: HttpContext) {
    try {
      const requesterUserId = auth.user!.id
      const { userId: targetUserId } = params
      const { startDate: startDateParam, endDate: endDateParam } = await request.validate(
        availabilityQueryValidator
      )

      // Parse dates
      const startDate = DateTime.fromISO(startDateParam)
      const endDate = DateTime.fromISO(endDateParam)

      if (!startDate.isValid || !endDate.isValid) {
        return response.badRequest(
          createErrorResponse('Invalid date format. Use YYYY-MM-DD format.', 'INVALID_DATE')
        )
      }

      if (startDate > endDate) {
        return response.badRequest(
          createErrorResponse('Start date must be before or equal to end date.', 'INVALID_DATE_RANGE')
        )
      }

      const availability = await this.availabilityService.getAvailabilityForDateRange(
        requesterUserId,
        targetUserId,
        startDate,
        endDate
      )

      return response.ok(createSuccessResponse(availability, 'Availability range retrieved successfully'))
    } catch (error) {
      if (error.message.includes('Date range cannot exceed 31 days')) {
        return response.badRequest(createErrorResponse(error.message, 'DATE_RANGE_TOO_LARGE'))
      }

      return response.internalServerError(
        createErrorResponse('Failed to retrieve availability range', 'AVAILABILITY_ERROR', error.message)
      )
    }
  }

  /**
   * GET /api/v1/availability/slots
   * 
   * Get the authenticated user's own availability slots.
   */
  async getUserAvailabilitySlots({ auth, request, response }: HttpContext) {
    try {
      const userId = auth.user!.id
      const query = request.qs()

      const slots = await this.slotService.getUserSlots(userId, query)

      return response.ok(createSuccessResponse(slots, 'Availability slots retrieved successfully'))
    } catch (error) {
      return response.internalServerError(
        createErrorResponse('Failed to retrieve availability slots', 'SLOTS_ERROR', error.message)
      )
    }
  }

  /**
   * POST /api/v1/availability/slots
   * 
   * Create a new availability slot for the authenticated user.
   */
  async createAvailabilitySlot({ auth, request, response }: HttpContext) {
    try {
      const userId = auth.user!.id
      const data = await request.validate(createAvailabilitySlotValidator)

      const slot = await this.slotService.createSlot(userId, data)

      return response.created(createSuccessResponse(slot, 'Availability slot created successfully'))
    } catch (error) {
      if (error.messages) {
        return response.badRequest(createErrorResponse('Validation failed', 'VALIDATION_ERROR', error.messages))
      }

      return response.internalServerError(
        createErrorResponse('Failed to create availability slot', 'CREATE_SLOT_ERROR', error.message)
      )
    }
  }

  /**
   * PUT /api/v1/availability/slots/:id
   * 
   * Update an existing availability slot.
   */
  async updateAvailabilitySlot({ params, auth, request, response }: HttpContext) {
    try {
      const userId = auth.user!.id
      const { id } = params
      const data = await request.validate(updateAvailabilitySlotValidator)

      const slot = await this.slotService.updateSlot(id, userId, data)

      return response.ok(createSuccessResponse(slot, 'Availability slot updated successfully'))
    } catch (error) {
      if (error.messages) {
        return response.badRequest(createErrorResponse('Validation failed', 'VALIDATION_ERROR', error.messages))
      }

      if (error.message.includes('not found')) {
        return response.notFound(createErrorResponse('Availability slot not found', 'SLOT_NOT_FOUND'))
      }

      return response.internalServerError(
        createErrorResponse('Failed to update availability slot', 'UPDATE_SLOT_ERROR', error.message)
      )
    }
  }

  /**
   * DELETE /api/v1/availability/slots/:id
   * 
   * Delete (soft delete) an availability slot.
   */
  async deleteAvailabilitySlot({ params, auth, response }: HttpContext) {
    try {
      const userId = auth.user!.id
      const { id } = params

      await this.slotService.deleteSlot(id, userId)

      return response.ok(createSuccessResponse(null, 'Availability slot deleted successfully'))
    } catch (error) {
      if (error.message.includes('not found')) {
        return response.notFound(createErrorResponse('Availability slot not found', 'SLOT_NOT_FOUND'))
      }

      return response.internalServerError(
        createErrorResponse('Failed to delete availability slot', 'DELETE_SLOT_ERROR', error.message)
      )
    }
  }

  /**
   * GET /api/v1/availability/slots/:id
   * 
   * Get a specific availability slot by ID.
   */
  async getAvailabilitySlot({ params, auth, response }: HttpContext) {
    try {
      const userId = auth.user!.id
      const { id } = params

      const slot = await this.slotService.getSlotById(id, userId)

      return response.ok(createSuccessResponse(slot, 'Availability slot retrieved successfully'))
    } catch (error) {
      if (error.message.includes('not found')) {
        return response.notFound(createErrorResponse('Availability slot not found', 'SLOT_NOT_FOUND'))
      }

      return response.internalServerError(
        createErrorResponse('Failed to retrieve availability slot', 'GET_SLOT_ERROR', error.message)
      )
    }
  }

  /**
   * POST /api/v1/availability/check
   * 
   * Check if a user is available during a specific time period.
   * Useful for scheduling and booking features.
   */
  async checkAvailability({ auth, request, response }: HttpContext) {
    try {
      const requesterUserId = auth.user!.id
      const { targetUserId, startDate, endDate } = request.only(['targetUserId', 'startDate', 'endDate'])

      // Parse dates
      const start = DateTime.fromISO(startDate)
      const end = DateTime.fromISO(endDate)

      if (!start.isValid || !end.isValid) {
        return response.badRequest(
          createErrorResponse('Invalid date format. Use ISO 8601 format.', 'INVALID_DATE')
        )
      }

      if (start >= end) {
        return response.badRequest(
          createErrorResponse('Start date must be before end date.', 'INVALID_DATE_RANGE')
        )
      }

      const isAvailable = await this.slotService.checkUserAvailability(
        requesterUserId,
        targetUserId,
        start,
        end
      )

      return response.ok(
        createSuccessResponse(
          { isAvailable, startDate, endDate, targetUserId },
          'Availability check completed successfully'
        )
      )
    } catch (error) {
      return response.internalServerError(
        createErrorResponse('Failed to check availability', 'AVAILABILITY_CHECK_ERROR', error.message)
      )
    }
  }
}
