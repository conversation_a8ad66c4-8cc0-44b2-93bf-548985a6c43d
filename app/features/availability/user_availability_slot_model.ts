import BaseModel from '#models/base_model'
import { column, belongsTo, computed } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import { compose } from '@adonisjs/core/helpers'
import { SoftDeletes } from 'adonis-lucid-soft-deletes'
import User from '#features/user/user_model'
import UserGroup from '#features/group/user_group_model'
import UserContactList from '#features/contact/lists/user_contact_list_model'
import { AvailabilityScopeType } from '#types/policy_template_types'

/**
 * UserAvailabilitySlot Model
 * 
 * Represents a user's availability slot, supporting both:
 * - Recurring availability (e.g., "Mon-Fri 9am-5pm")
 * - Specific one-time availability (e.g., "Dec 25 unavailable")
 * 
 * Integrates with the privacy policy system for visibility control.
 */
export default class UserAvailabilitySlot extends compose(BaseModel, SoftDeletes) {
  static table = 'user_availability_slots'

  @column({ isPrimary: true })
  declare id: string

  @column()
  declare userId: string

  @column()
  declare title: string

  @column()
  declare description: string | null

  // Recurring availability fields
  @column()
  declare dayOfWeek: number | null // 0-6 (Sunday-Saturday)

  @column()
  declare startTime: string | null // HH:MM format

  @column()
  declare endTime: string | null // HH:MM format

  @column.date()
  declare recurringStartDate: DateTime | null

  @column.date()
  declare recurringEndDate: DateTime | null

  // Specific availability fields
  @column.dateTime()
  declare specificStartDate: DateTime | null

  @column.dateTime()
  declare specificEndDate: DateTime | null

  // Availability state
  @column()
  declare isAvailable: boolean

  // Visibility controls
  @column()
  declare visibilityScope: AvailabilityScopeType

  @column()
  declare visibilityTargetUserId: string | null

  @column()
  declare visibilityTargetGroupId: string | null

  @column()
  declare visibilityTargetContactListId: string | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @column.dateTime()
  declare deletedAt: DateTime | null

  // Relationships
  @belongsTo(() => User, {
    foreignKey: 'userId',
  })
  declare user: BelongsTo<typeof User>

  @belongsTo(() => User, {
    foreignKey: 'visibilityTargetUserId',
  })
  declare visibilityTargetUser: BelongsTo<typeof User>

  @belongsTo(() => UserGroup, {
    foreignKey: 'visibilityTargetGroupId',
  })
  declare visibilityTargetGroup: BelongsTo<typeof UserGroup>

  @belongsTo(() => UserContactList, {
    foreignKey: 'visibilityTargetContactListId',
  })
  declare visibilityTargetContactList: BelongsTo<typeof UserContactList>

  // Computed properties
  @computed()
  get isRecurring(): boolean {
    return this.dayOfWeek !== null
  }

  @computed()
  get isSpecific(): boolean {
    return this.specificStartDate !== null
  }

  @computed()
  get slotType(): 'recurring' | 'specific' {
    return this.isRecurring ? 'recurring' : 'specific'
  }

  @computed()
  get dayName(): string | null {
    if (this.dayOfWeek === null) return null
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
    return days[this.dayOfWeek]
  }

  // Validation helpers
  validateSlotType(): boolean {
    // Exactly one of recurring or specific must be set
    const hasRecurring = this.dayOfWeek !== null
    const hasSpecific = this.specificStartDate !== null
    return hasRecurring !== hasSpecific // XOR logic
  }

  validateTimeOrder(): boolean {
    if (this.isRecurring && this.startTime && this.endTime) {
      return this.startTime < this.endTime
    }
    if (this.isSpecific && this.specificStartDate && this.specificEndDate) {
      return this.specificStartDate < this.specificEndDate
    }
    return true
  }

  validateRecurringDateRange(): boolean {
    if (this.recurringStartDate && this.recurringEndDate) {
      return this.recurringStartDate <= this.recurringEndDate
    }
    return true
  }

  // Business logic methods
  isActiveOnDate(date: DateTime): boolean {
    if (this.isRecurring) {
      // Check if date falls within recurring pattern
      if (this.dayOfWeek !== date.weekday) return false
      
      if (this.recurringStartDate && date < this.recurringStartDate) return false
      if (this.recurringEndDate && date > this.recurringEndDate) return false
      
      return true
    }

    if (this.isSpecific && this.specificStartDate && this.specificEndDate) {
      const dateStart = date.startOf('day')
      const slotStart = this.specificStartDate.startOf('day')
      const slotEnd = this.specificEndDate.startOf('day')
      
      return dateStart >= slotStart && dateStart <= slotEnd
    }

    return false
  }

  getTimeRangeForDate(date: DateTime): { start: DateTime; end: DateTime } | null {
    if (!this.isActiveOnDate(date)) return null

    if (this.isRecurring && this.startTime && this.endTime) {
      const [startHour, startMinute] = this.startTime.split(':').map(Number)
      const [endHour, endMinute] = this.endTime.split(':').map(Number)
      
      return {
        start: date.set({ hour: startHour, minute: startMinute, second: 0, millisecond: 0 }),
        end: date.set({ hour: endHour, minute: endMinute, second: 0, millisecond: 0 }),
      }
    }

    if (this.isSpecific && this.specificStartDate && this.specificEndDate) {
      return {
        start: this.specificStartDate,
        end: this.specificEndDate,
      }
    }

    return null
  }

  // Privacy visibility check
  isVisibleToUser(viewerUserId: string, relationshipContext: any): boolean {
    switch (this.visibilityScope) {
      case 'public':
        return true
      
      case 'all_contacts':
        return relationshipContext.isContact
      
      case 'specific_contact':
        return this.visibilityTargetUserId === viewerUserId
      
      case 'specific_group':
        return relationshipContext.groupIds?.includes(this.visibilityTargetGroupId)
      
      case 'specific_contact_list':
        return relationshipContext.contactListIds?.includes(this.visibilityTargetContactListId)
      
      default:
        return false
    }
  }

  // Serialization for API responses
  toJSON() {
    const json = super.toJSON()
    
    // Add computed properties
    json.isRecurring = this.isRecurring
    json.isSpecific = this.isSpecific
    json.slotType = this.slotType
    json.dayName = this.dayName
    
    return json
  }
}
