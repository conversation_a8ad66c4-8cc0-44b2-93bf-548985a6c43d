import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'user_availability_slots'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      // Primary key
      table.uuid('id').primary().defaultTo(this.db.rawQuery('gen_random_uuid()').knexQuery)

      // Foreign key to users table
      table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE').notNullable()

      // Descriptive information
      table.string('title').notNullable()
      table.text('description').nullable()

      // Temporal fields for recurring availability
      table.integer('day_of_week').nullable() // 0-6 (Sunday-Saturday), NULL for specific dates
      table.time('start_time').nullable()
      table.time('end_time').nullable()
      table.date('recurring_start_date').nullable() // When recurring pattern starts
      table.date('recurring_end_date').nullable() // When recurring pattern ends

      // Temporal fields for specific one-time availability
      table.timestamp('specific_start_date').nullable()
      table.timestamp('specific_end_date').nullable()

      // Availability state
      table.boolean('is_available').defaultTo(true).notNullable()

      // Privacy/Visibility controls
      table
        .enum('visibility_scope', [
          'public',
          'all_contacts',
          'specific_contact',
          'specific_group',
          'specific_contact_list',
        ])
        .defaultTo('public')
        .notNullable()

      // Nullable foreign keys for specific visibility targeting
      table.uuid('visibility_target_user_id').references('id').inTable('users').nullable()
      table.uuid('visibility_target_group_id').references('id').inTable('user_groups').nullable()
      table
        .uuid('visibility_target_contact_list_id')
        .references('id')
        .inTable('user_contact_lists')
        .nullable()

      // Timestamps
      table.timestamp('created_at').notNullable().defaultTo(this.now())
      table.timestamp('updated_at').notNullable().defaultTo(this.now())
      table.timestamp('deleted_at').nullable() // Soft deletes

      // Constraints
      table.check(
        '(day_of_week IS NOT NULL AND specific_start_date IS NULL) OR (day_of_week IS NULL AND specific_start_date IS NOT NULL)',
        'check_availability_type'
      )

      table.check('start_time < end_time OR (start_time IS NULL AND end_time IS NULL)', 'check_time_order')

      table.check(
        'specific_start_date < specific_end_date OR (specific_start_date IS NULL AND specific_end_date IS NULL)',
        'check_specific_date_order'
      )

      table.check(
        'recurring_start_date <= recurring_end_date OR (recurring_start_date IS NULL AND recurring_end_date IS NULL)',
        'check_recurring_date_order'
      )

      // Performance indexes
      table.index(['user_id', 'day_of_week'], 'idx_user_availability_recurring')
      table.index(['user_id', 'specific_start_date'], 'idx_user_availability_specific')
      table.index(['user_id', 'is_available'], 'idx_user_availability_state')
      table.index(['visibility_scope'], 'idx_availability_visibility_scope')

      // Partial indexes for better performance on nullable foreign keys
      table.index(['visibility_target_user_id'], 'idx_availability_target_user', {
        where: 'visibility_target_user_id IS NOT NULL',
      })
      table.index(['visibility_target_group_id'], 'idx_availability_target_group', {
        where: 'visibility_target_group_id IS NOT NULL',
      })
      table.index(['visibility_target_contact_list_id'], 'idx_availability_target_contact_list', {
        where: 'visibility_target_contact_list_id IS NOT NULL',
      })

      // Composite index for efficient calendar queries
      table.index(
        ['user_id', 'day_of_week', 'start_time', 'end_time'],
        'idx_user_availability_calendar_recurring'
      )
      table.index(
        ['user_id', 'specific_start_date', 'specific_end_date'],
        'idx_user_availability_calendar_specific'
      )

      // Soft delete index
      table.index(['deleted_at'], 'idx_availability_soft_delete')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
