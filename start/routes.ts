/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'

const AITaskController = () => import('#features/task/tasks_controller')
const HealthController = () => import('#controllers/health_controller')
const SessionController = () => import('#features/session/session_controller')
const ProfileController = () => import('#features/profile/profile_controller')
const ProfileSettingsController = () => import('#features/profile/profile_settings_controller')
const EventController = () => import('#features/event/event_controller')
const PolicyCategoryController = () =>
  import('#features/privacy-policy/category/policy_category_controller')
const PrivacyPolicyTemplateController = () =>
  import('#features/privacy-policy/template/privacy_policy_template_controller')
const ContactController = () => import('#features/contact/contact_controller')
const PolicyTemplateRuleController = () =>
  import('#features/privacy-policy/rule/policy_template_rule_controller')
const GroupController = () => import('#features/group/groups/group_controller')
const GroupMemberController = () => import('#features/group/members/group_member_controller')
const GroupInvitationController = () =>
  import('#features/group/invitations/group_invitation_controller')
const CommitmentController = () => import('#features/commitment/commitment_controller')
const AvailabilityController = () => import('#features/availability/availability_controller')

router.get('/', async () => {
  return {
    hello: 'world',
  }
})

router.get('/health', [HealthController, 'check'])

router
  .group(() => {
    router.get('/rate-limiter-test', async () => {
      return {
        test: 'hi',
      }
    })

    // Auth routes
    router
      .group(() => {
        // Public routes
        router.post('register', [SessionController, 'register'])
        router.post('login', [SessionController, 'login'])
        router.post('refresh', [SessionController, 'refresh'])
      })
      .prefix('/auth')

    // Protected routes
    router
      .group(() => {
        // router.get('profile', [SessionController, 'profile'])
        router.post('logout', [SessionController, 'logout'])

        router
          .group(() => {
            router.post('prompt', [AITaskController, 'store'])
          })
          .prefix('/ai')

        // Profile routes
        router
          .group(() => {
            router.post('/', [ProfileController, 'store'])
            router.get('/', [ProfileController, 'show'])
            router.patch('/', [ProfileController, 'update'])
            router.delete('/', [ProfileController, 'destroy'])
          })
          .prefix('/profile')

        // Profile Settings routes
        router
          .group(() => {
            router.post('/', [ProfileSettingsController, 'store'])
            router.get('/:id', [ProfileSettingsController, 'show'])
            router.put('/:id', [ProfileSettingsController, 'update'])
            router.delete('/:id', [ProfileSettingsController, 'destroy'])
          })
          .prefix('/profile-settings')

        // Tasks routes
        router.group(() => {
          // Resource route for individual task CRUD operations
          router.resource('/task', AITaskController).apiOnly()

          // Dedicated route for listing all tasks with query parameters
          router.get('/tasks', [AITaskController, 'index'])
        })

        // Task invitation routes
        router.post('/tasks/:id/invitations', [CommitmentController, 'storeTaskInvitation'])
        router.get('/tasks/:id/invitations', [CommitmentController, 'indexTaskCommitments'])

        // Events routes
        router.group(() => {
          // Resource route for individual event CRUD operations
          router.resource('/event', EventController).apiOnly()

          // Dedicated route for listings all events with query parameters
          router.get('/events', [EventController, 'index'])
        })

        // Event invitation routes
        router.post('/events/:id/invitations', [CommitmentController, 'storeEventInvitation'])
        router.get('/events/:id/invitations', [CommitmentController, 'indexEventCommitments'])

        // Policy Categories routes
        router.group(() => {
          router.resource('/privacy-profile-categories', PolicyCategoryController).apiOnly()
        })

        // Privacy Policy Templates routes
        router.group(() => {
          router.resource('/privacy-profiles', PrivacyPolicyTemplateController).apiOnly()
        })

        // Privacy Profile Rules routes
        router.group(() => {
          router.resource('/privacy-profile-rules', PolicyTemplateRuleController).apiOnly()
        })

        // Contact Management routes
        router
          .group(() => {
            // Contact request routes
            router.post('/requests', [ContactController, 'sendContactRequest'])
            router.patch('/requests/:contactId/respond', [
              ContactController,
              'respondToContactRequest',
            ])
            router.patch('/:contactId/block', [ContactController, 'blockContact'])
            router.get('/', [ContactController, 'getContacts'])

            router
              .group(() => {
                // Contact list routes
                router.post('/', [ContactController, 'createContactList'])
                router.get('/', [ContactController, 'getContactLists'])
                router.get('/:listId', [ContactController, 'getContactList'])
                router.patch('/:listId', [ContactController, 'updateContactList'])
                router.delete('/:listId', [ContactController, 'deleteContactList'])
                // Contact list member routes
                router.post('/:listId/members', [ContactController, 'addContactToList'])
                // Remove a contact from any list by member ID
                router.delete('/members/:memberId', [ContactController, 'removeContactFromList'])
                router.get('/:listId/members', [ContactController, 'getContactListMembers'])
              })
              .prefix('/lists')
          })
          .prefix('/contacts')

        // Commitment Management routes (unified invitation system)
        router
          .group(() => {
            // User-centric invitation endpoints
            router.get('/received', [CommitmentController, 'indexReceived'])
            router.get('/sent', [CommitmentController, 'indexSent'])

            // Global invitation response endpoints
            router.put('/:id', [CommitmentController, 'update'])
            router.delete('/:id', [CommitmentController, 'destroy'])
          })
          .prefix('/invitations')

        // Group Management routes
        router
          .group(() => {
            // User-specific group endpoints (must come before parameterized routes)
            router.get('/my-invitations', [GroupInvitationController, 'getMyInvitations'])
            router.get('/my-requests', [GroupInvitationController, 'getMyRequests'])

            // Global invitation response endpoint
            router.put('/invitations/:invitationId', [GroupInvitationController, 'update'])

            // Core group CRUD operations
            router.post('/', [GroupController, 'store'])
            router.get('/', [GroupController, 'index'])
            router.get('/:id', [GroupController, 'show'])
            router.put('/:id', [GroupController, 'update'])
            router.delete('/:id', [GroupController, 'destroy'])

            // Group member management
            router.get('/:id/members', [GroupMemberController, 'index'])
            router.post('/:id/members', [GroupMemberController, 'store'])
            router.put('/:id/members/:userId', [GroupMemberController, 'update'])
            router.delete('/:id/members/:userId', [GroupMemberController, 'destroy'])

            // Group invitation management
            router.get('/:id/invitations', [GroupInvitationController, 'index'])
            router.post('/:id/invitations', [GroupInvitationController, 'store'])
            router.post('/:id/join-requests', [GroupInvitationController, 'createJoinRequest'])
          })
          .prefix('/groups')

        // Availability routes
        router
          .group(() => {
            // Get availability for a specific user and date
            router.get('/:userId/:date', [AvailabilityController, 'getAvailabilityForDay'])

            // Get availability for a user across a date range (calendar view)
            router.get('/:userId/range', [AvailabilityController, 'getAvailabilityForRange'])

            // Check if a user is available during a specific time period
            router.post('/check', [AvailabilityController, 'checkAvailability'])

            // Availability slot management
            router
              .group(() => {
                router.get('/', [AvailabilityController, 'getUserAvailabilitySlots'])
                router.post('/', [AvailabilityController, 'createAvailabilitySlot'])
                router.get('/:id', [AvailabilityController, 'getAvailabilitySlot'])
                router.put('/:id', [AvailabilityController, 'updateAvailabilitySlot'])
                router.delete('/:id', [AvailabilityController, 'deleteAvailabilitySlot'])
              })
              .prefix('/slots')
          })
          .prefix('/availability')
      })
      .use(middleware.auth({ guards: ['api'] }))
  })
  .prefix('/api/v1')
  .use(
    process.env.NODE_ENV === 'test' || process.env.NODE_ENV === 'development'
      ? (ctx, next) => next()
      : middleware.throttle()
  )
