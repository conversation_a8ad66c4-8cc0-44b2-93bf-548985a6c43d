# Privacy Policy System Testing Guide

This guide provides comprehensive testing scenarios for the SkedAI privacy policy system using the provided sample data.

## Overview

The privacy policy system provides sophisticated privacy controls for activity visibility in SkedAI. This testing guide covers:

1. **API Testing** - Testing CRUD operations for categories and templates
2. **Policy Evaluation** - Testing visibility rule evaluation
3. **Edge Cases** - Testing conflict resolution and edge scenarios
4. **Performance Testing** - Testing the optimized query implementation

## Test Data Setup

### 1. Load Sample Data

```bash
# Load the sample data into your test database
psql -d skedai_test -f sample_privacy_policy_test_suite.sql
```

### 2. Verify Data Loading

```sql
-- Check data was loaded correctly
SELECT 'Policy Categories' as table_name, COUNT(*) as count
FROM policy_categories WHERE id LIKE 'cat-%'
UNION ALL
SELECT 'Privacy Policy Templates' as table_name, COUNT(*) as count
FROM privacy_policy_templates WHERE id LIKE 'tpl-%'
UNION ALL
SELECT 'Policy Template Rules' as table_name, COUNT(*) as count
FROM policy_template_rules WHERE id LIKE 'rule-%'
UNION ALL
SELECT 'Privacy Assignments' as table_name, COUNT(*) as count
FROM privacy_assignments WHERE id LIKE 'assign-%';
```

Expected output:

- Policy Categories: 6
- Privacy Policy Templates: 11
- Policy Template Rules: 15
- Privacy Assignments: 6

## API Testing

### 1. Policy Categories API

#### Create Category

```bash
curl -X POST http://localhost:3333/api/privacy-policies/categories \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "Executive Time",
    "description": "High-level executive meetings and strategic planning"
  }'
```

#### List Categories

```bash
curl -X GET "http://localhost:3333/api/privacy-policies/categories?sort=displayOrder" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Update Category

```bash
curl -X PUT http://localhost:3333/api/privacy-policies/categories/cat-work-001 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "description": "Updated description for work activities"
  }'
```

### 2. Privacy Policy Templates API

#### Create Template

```bash
curl -X POST http://localhost:3333/api/privacy-policies/templates \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "Executive - Board Level",
    "description": "Maximum privacy for board and C-level discussions",
    "permissions": {
      "defaultVisibility": "hidden",
      "allowOverrides": false
    },
    "isDefault": false,
    "policyCategoryId": "cat-work-001"
  }'
```

#### List Templates

```bash
curl -X GET "http://localhost:3333/api/privacy-policies/templates?policyCategory=cat-work-001&isDefault=true" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Query Templates with Filters

```bash
# Get all work templates
curl -X GET "http://localhost:3333/api/privacy-policies/templates?policyCategory=cat-work-001" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Get default templates only
curl -X GET "http://localhost:3333/api/privacy-policies/templates?isDefault=true" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Policy Evaluation Testing

### 1. Work Meeting Visibility Test

**Scenario**: Test how a work meeting appears to different viewer types

**Test Data**:

- Activity: `activity-001` (Team Standup Meeting)
- Template: `tpl-work-internal`

**Expected Results**:

| Viewer Type                            | Visibility Level | Title                  | Description      | Custom Message      |
| -------------------------------------- | ---------------- | ---------------------- | ---------------- | ------------------- |
| Team Member (`profile-dev-001`)        | `full_details`   | "Team Standup Meeting" | Full description | null                |
| Work Contact (`profile-colleague-001`) | `title_only`     | "Team Standup Meeting" | null             | "In a work meeting" |
| Public Viewer                          | `title_only`     | "Team Standup Meeting" | null             | "In a work meeting" |

**Test Implementation**:

```typescript
// Test team member access
const teamMemberResult = await availabilityService.getAvailabilityForDay(
  'profile-dev-001', // team member as requester
  'profile-user-001', // activity owner
  DateTime.fromISO('2024-03-20')
)

// Test work contact access
const workContactResult = await availabilityService.getAvailabilityForDay(
  'profile-colleague-001', // work contact as requester
  'profile-user-001', // activity owner
  DateTime.fromISO('2024-03-20')
)
```

### 2. Medical Appointment Privacy Test

**Scenario**: Test maximum privacy for medical appointments

**Test Data**:

- Activity: `activity-002` (Doctor Appointment)
- Template: `tpl-medical-strict`

**Expected Results**:

| Viewer Type                   | Visibility Level | Title                | Description | Custom Message        |
| ----------------------------- | ---------------- | -------------------- | ----------- | --------------------- |
| Spouse (`profile-spouse-001`) | `title_only`     | "Doctor Appointment" | null        | null                  |
| Work Contact                  | `busy_only`      | "Busy"               | null        | "Medical appointment" |
| Public Viewer                 | `busy_only`      | "Busy"               | null        | "Medical appointment" |

### 3. Family Event with Override Test

**Scenario**: Test family event with assignment-level overrides

**Test Data**:

- Activity: `activity-003` (Family Dinner)
- Template: `tpl-family-shared` with override to `title_only`
- Assignment Override: `overrideDetailVisibility: "title_only"`, `overrideCustomMessage: "Family celebration"`

**Expected Results**:

| Viewer Type                          | Visibility Level | Title           | Description | Custom Message       |
| ------------------------------------ | ---------------- | --------------- | ----------- | -------------------- |
| Family Member (`profile-spouse-001`) | `title_only`     | "Family Dinner" | null        | "Family celebration" |
| Work Contact                         | `busy_only`      | "Busy"          | null        | "Family celebration" |

## Edge Case Testing

### 1. Priority Conflict Resolution

**Scenario**: Test rule priority when multiple rules could apply

**Test Data**: Template `tpl-test-conflicts` with three overlapping rules:

- High priority (30): Specific contact gets `full_details`
- Medium priority (20): Group members get `title_only`
- Low priority (10): All contacts get `busy_only`

**Test Case**: Manager (`profile-manager-001`) who is both a specific contact AND a group member

**Expected Result**: Should get `full_details` due to higher priority specific contact rule

### 2. No Rules Template

**Scenario**: Test template with no specific rules (uses template defaults)

**Test Data**: Template `tpl-test-no-rules` with no policy rules

**Expected Result**: Should use template's `defaultDetailVisibility` and `defaultCustomMessage`

### 3. Assignment Override Testing

**Scenario**: Test assignment-level overrides superseding template defaults

**Test Data**: `activity-test-override` with overrides:

- `overrideBlocksScheduling: false`
- `overrideDetailVisibility: "full_details"`
- `overrideCustomMessage: "Override message for testing"`

**Expected Result**: Should use override values instead of template defaults

## Performance Testing

### 1. Cache Performance Test

Test the Redis caching optimization:

```typescript
// First request (cache miss) - should be 3 queries
const startTime1 = Date.now()
const result1 = await availabilityService.getAvailabilityForDay(
  'profile-dev-001',
  'profile-user-001',
  DateTime.fromISO('2024-03-20')
)
const time1 = Date.now() - startTime1
console.log(`Cache miss: ${time1}ms, queries: ${result1.cacheHit ? 2 : 3}`)

// Second request (cache hit) - should be 2 queries
const startTime2 = Date.now()
const result2 = await availabilityService.getAvailabilityForDay(
  'profile-dev-001',
  'profile-user-001',
  DateTime.fromISO('2024-03-21')
)
const time2 = Date.now() - startTime2
console.log(`Cache hit: ${time2}ms, queries: ${result2.cacheHit ? 2 : 3}`)
```

### 2. Batch Request Performance

Test multi-day requests with caching:

```typescript
const result = await availabilityController.getAvailabilityRange({
  params: {
    profileId: 'profile-user-001',
    startDate: '2024-03-20',
    endDate: '2024-03-26',
  },
  auth: { user: { profile: { id: 'profile-dev-001' } } },
})

console.log('Performance metrics:', result.performance)
// Expected: First day = 3 queries, subsequent days = 2 queries each
```

## Test Automation Scripts

### Jest Test Suite Example

```typescript
describe('Privacy Policy System', () => {
  beforeEach(async () => {
    // Load test data
    await loadSampleData()
  })

  describe('Policy Evaluation', () => {
    it('should respect team member visibility for work meetings', async () => {
      const result = await getActivityVisibility(
        'activity-001',
        'profile-dev-001' // team member
      )

      expect(result.visibility).toBe('full_details')
      expect(result.title).toBe('Team Standup Meeting')
      expect(result.description).toBeTruthy()
    })

    it('should show only title to work contacts for internal meetings', async () => {
      const result = await getActivityVisibility(
        'activity-001',
        'profile-colleague-001' // work contact
      )

      expect(result.visibility).toBe('title_only')
      expect(result.title).toBe('Team Standup Meeting')
      expect(result.description).toBeNull()
      expect(result.customMessage).toBe('In a work meeting')
    })

    it('should hide medical appointments from work contacts', async () => {
      const result = await getActivityVisibility(
        'activity-002',
        'profile-colleague-001' // work contact
      )

      expect(result.visibility).toBe('busy_only')
      expect(result.title).toBe('Busy')
      expect(result.customMessage).toBe('Medical appointment')
    })
  })

  describe('Priority Resolution', () => {
    it('should apply highest priority rule when multiple rules match', async () => {
      const result = await getActivityVisibility(
        'activity-conflict-test',
        'profile-manager-001' // matches both specific contact and group rules
      )

      // Should use high priority specific contact rule (full_details)
      // instead of medium priority group rule (title_only)
      expect(result.visibility).toBe('full_details')
      expect(result.customMessage).toBe('High priority rule')
    })
  })

  describe('Assignment Overrides', () => {
    it('should respect assignment-level overrides', async () => {
      const result = await getActivityVisibility('activity-test-override', 'profile-colleague-001')

      expect(result.visibility).toBe('full_details') // Override from assignment
      expect(result.blocksScheduling).toBe(false) // Override from assignment
      expect(result.customMessage).toBe('Override message for testing')
    })
  })
})
```

## API Validation Testing

### Input Validation Tests

```typescript
describe('API Validation', () => {
  it('should validate category creation with missing name', async () => {
    const response = await request(app)
      .post('/api/privacy-policies/categories')
      .send({ description: 'Missing name field' })
      .expect(400)

    expect(response.body.errors).toContain('name is required')
  })

  it('should validate template creation with invalid UUID', async () => {
    const response = await request(app)
      .post('/api/privacy-policies/templates')
      .send({
        name: 'Test Template',
        policyCategoryId: 'invalid-uuid',
      })
      .expect(400)

    expect(response.body.errors).toContain('policyCategoryId must be a valid UUID')
  })
})
```

## Database Query Performance Testing

### Query Optimization Verification

```sql
-- Test query performance with EXPLAIN ANALYZE
EXPLAIN ANALYZE
SELECT a.*,
       pa.override_detail_visibility,
       pt.default_detail_visibility,
       ptr.detail_visibility as rule_visibility,
       ptr.priority
FROM activities a
LEFT JOIN privacy_policy_assignments pa ON a.id = pa.activity_id
LEFT JOIN privacy_policy_templates pt ON pa.template_id = pt.id
LEFT JOIN policy_template_rules ptr ON pt.id = ptr.template_id
WHERE a.user_id = 'user-001'
  AND DATE(a.start_date) = '2024-03-20'
ORDER BY ptr.priority DESC;
```

Expected performance characteristics:

- Index usage for activity lookups
- Efficient JOIN operations
- Sub-second response times for typical datasets

## Troubleshooting Common Issues

### 1. Cache Invalidation Not Working

**Symptoms**: Outdated relationship context being returned

**Solution**:

```typescript
// Manual cache clear for testing
await Redis.del('rel_ctx:profile-user-001:profile-dev-001')
await Redis.del('rel_ctx:profile-dev-001:profile-user-001')
```

### 2. Policy Rules Not Applying

**Check**:

1. Rule priority ordering
2. Viewer scope type matching
3. Target ID references (profile, group, contact list)

### 3. Performance Issues

**Debug**:

1. Check Redis connection
2. Verify database indexes exist
3. Monitor query execution plans
4. Check for N+1 query problems

## Integration Testing

Test the complete flow from API request to policy evaluation:

```typescript
describe('End-to-End Privacy Policy Flow', () => {
  it('should create category, template, and rules, then evaluate correctly', async () => {
    // 1. Create category
    const category = await createCategory({
      name: 'Test Category',
      description: 'Testing category',
    })

    // 2. Create template
    const template = await createTemplate({
      name: 'Test Template',
      categorId: category.id,
      defaultDetailVisibility: 'title_only',
    })

    // 3. Create rule
    const rule = await createTemplateRule({
      templateId: template.id,
      viewerScopeType: 'all_contacts',
      detailVisibility: 'full_details',
      priority: 10,
    })

    // 4. Assign to activity
    const assignment = await createPolicyAssignment({
      activityId: 'test-activity',
      templateId: template.id,
    })

    // 5. Test visibility evaluation
    const result = await getActivityVisibility('test-activity', 'profile-contact-001')

    expect(result.visibility).toBe('full_details')
  })
})
```

This comprehensive testing approach ensures the privacy policy system works correctly across all scenarios and maintains high performance standards.
